# -*- coding: utf-8 -*-
"""
最优插值(OI)方法实现
基于数据同化理论，结合背景场和观测数据
"""

import numpy as np
from scipy.spatial.distance import cdist
from scipy.linalg import solve, LinAlgError
from typing import Dict, Tuple, Optional, List
import logging

logger = logging.getLogger(__name__)


class OIInterpolator:
    """最优插值器"""
    
    def __init__(self, config: Dict):
        """
        初始化OI插值器
        
        Args:
            config: 配置字典
        """
        self.config = config
        self.method_params = config['method_params']['OI']
        
        # OI参数
        self.observation_error_variance = self.method_params['observation_error_variance']
        self.background_error_corr_length = self.method_params['background_error_corr_length']
        
        # 当前优化的参数（如果有）
        self._optimized_params = None
    
    def set_optimized_parameters(self, params: List[float]) -> None:
        """
        设置优化后的参数
        
        Args:
            params: [observation_error_variance, background_error_corr_length] 参数列表
        """
        if len(params) != 2:
            raise ValueError("OI参数必须包含2个值: [observation_error_variance, background_error_corr_length]")
        
        self._optimized_params = params
        logger.debug(f"设置OI优化参数: obs_error_var={params[0]:.3f}, corr_length={params[1]:.1f}")
    
    def interpolate(self, station_coords: np.ndarray, station_values: np.ndarray,
                   grid_x: np.ndarray, grid_y: np.ndarray, 
                   mask: np.ndarray) -> np.ndarray:
        """
        执行OI插值
        
        Args:
            station_coords: 站点坐标数组 (n_stations, 2)
            station_values: 站点值数组 (n_stations,)
            grid_x: 栅格x坐标
            grid_y: 栅格y坐标
            mask: 掩膜数组
            
        Returns:
            插值结果数组
        """
        if len(station_coords) == 0 or len(station_values) == 0:
            logger.warning("没有有效的站点数据进行OI插值")
            return np.full_like(grid_x, np.nan)
        
        # 检查是否所有站点值都为零
        if np.all(station_values == 0):
            logger.debug("所有站点值为零，返回零值栅格")
            return np.zeros_like(grid_x)
        
        # 移除无效值
        valid_mask = ~np.isnan(station_values)
        if not np.any(valid_mask):
            logger.warning("所有站点值都是NaN")
            return np.full_like(grid_x, np.nan)
        
        station_coords = station_coords[valid_mask]
        station_values = station_values[valid_mask]
        
        # 检查站点数量
        if len(station_coords) < 2:
            logger.warning(f"站点数量太少({len(station_coords)})，无法进行OI插值")
            return np.full_like(grid_x, np.nan)
        
        try:
            return self._perform_oi(station_coords, station_values, grid_x, grid_y, mask)
        
        except Exception as e:
            logger.error(f"OI插值失败: {e}")
            # 降级到IDW方法
            logger.info("降级使用IDW方法")
            return self._fallback_to_idw(station_coords, station_values, grid_x, grid_y, mask)
    
    def _perform_oi(self, station_coords: np.ndarray, station_values: np.ndarray,
                   grid_x: np.ndarray, grid_y: np.ndarray, 
                   mask: np.ndarray) -> np.ndarray:
        """
        执行OI插值的核心逻辑
        """
        # 步骤1: 生成背景场（使用IDW）
        background_field = self._generate_background_field(station_coords, station_values, 
                                                          grid_x, grid_y, mask)
        
        # 步骤2: 计算背景场在观测点的值
        background_at_obs = self._interpolate_background_to_observations(
            background_field, grid_x, grid_y, station_coords)
        
        # 步骤3: 计算新息（观测值 - 背景场值）
        innovation = station_values - background_at_obs
        
        # 步骤4: 构建误差协方差矩阵
        R_matrix = self._build_observation_error_matrix(station_coords)
        B_matrix = self._build_background_error_matrix(station_coords)
        
        # 步骤5: 计算增益矩阵 W = B * H^T * (H * B * H^T + R)^(-1)
        # 在我们的情况下，H是单位矩阵（观测算子），所以简化为：
        # W = B * (B + R)^(-1)
        try:
            combined_matrix = B_matrix + R_matrix
            
            # 添加正则化以提高数值稳定性
            regularization = 1e-6 * np.eye(len(station_coords))
            combined_matrix += regularization
            
            # 计算权重矩阵
            weights = solve(combined_matrix, B_matrix)
            
        except LinAlgError as e:
            logger.error(f"矩阵求解失败: {e}")
            raise
        
        # 步骤6: 计算每个网格点的分析场
        result = self._apply_oi_correction(background_field, grid_x, grid_y, mask,
                                         station_coords, innovation, weights)
        
        return result
    
    def _generate_background_field(self, station_coords: np.ndarray, station_values: np.ndarray,
                                 grid_x: np.ndarray, grid_y: np.ndarray, 
                                 mask: np.ndarray) -> np.ndarray:
        """
        使用IDW生成背景场
        """
        from .idw import IDWInterpolator
        
        # 创建临时IDW配置
        idw_config = {
            'method_params': {'IDW': {'power': 2.0}},
            'run_settings': {'cpu_cores': 1},
            'data_processing': {'memory_management': {'use_shared_memory': False}}
        }
        
        idw_interpolator = IDWInterpolator(idw_config)
        background_field = idw_interpolator.interpolate(station_coords, station_values, 
                                                       grid_x, grid_y, mask)
        
        return background_field
    
    def _interpolate_background_to_observations(self, background_field: np.ndarray,
                                              grid_x: np.ndarray, grid_y: np.ndarray,
                                              station_coords: np.ndarray) -> np.ndarray:
        """
        将背景场插值到观测点位置
        """
        background_at_obs = np.zeros(len(station_coords))
        
        for i, (obs_x, obs_y) in enumerate(station_coords):
            # 找到最近的网格点
            distances = np.sqrt((grid_x - obs_x)**2 + (grid_y - obs_y)**2)
            min_idx = np.unravel_index(np.argmin(distances), distances.shape)
            
            background_at_obs[i] = background_field[min_idx]
        
        return background_at_obs
    
    def _build_observation_error_matrix(self, station_coords: np.ndarray) -> np.ndarray:
        """
        构建观测误差协方差矩阵R
        """
        # 获取当前参数
        if self._optimized_params is not None:
            obs_error_var = self._optimized_params[0]
        else:
            obs_error_var = self.observation_error_variance
        
        n_obs = len(station_coords)
        
        # 假设观测误差相互独立，R为对角矩阵
        R_matrix = np.eye(n_obs) * obs_error_var
        
        return R_matrix
    
    def _build_background_error_matrix(self, station_coords: np.ndarray) -> np.ndarray:
        """
        构建背景场误差协方差矩阵B
        """
        # 获取当前参数
        if self._optimized_params is not None:
            corr_length = self._optimized_params[1]
        else:
            corr_length = self.background_error_corr_length
        
        n_obs = len(station_coords)
        
        # 计算站点间距离
        distances = cdist(station_coords, station_coords)
        
        # 使用指数衰减模型计算背景场误差相关性
        # B_ij = sigma_b^2 * exp(-d_ij / L)
        
        # 估算背景场误差方差（简化假设）
        sigma_b_squared = 1.0  # 可以根据实际情况调整
        
        B_matrix = sigma_b_squared * np.exp(-distances / corr_length)
        
        return B_matrix
    
    def _apply_oi_correction(self, background_field: np.ndarray,
                           grid_x: np.ndarray, grid_y: np.ndarray, mask: np.ndarray,
                           station_coords: np.ndarray, innovation: np.ndarray,
                           weights: np.ndarray) -> np.ndarray:
        """
        应用OI修正到背景场
        """
        result = background_field.copy()
        
        # 获取有效区域
        valid_mask = ~np.isnan(mask) & (mask != 0)
        
        if not np.any(valid_mask):
            return result
        
        # 对每个有效网格点应用OI修正
        valid_indices = np.where(valid_mask)
        
        for i, j in zip(valid_indices[0], valid_indices[1]):
            grid_point = np.array([[grid_x[i, j], grid_y[i, j]]])
            
            # 计算网格点到观测点的距离
            distances_to_obs = cdist(grid_point, station_coords).flatten()
            
            # 计算背景场误差相关性
            if self._optimized_params is not None:
                corr_length = self._optimized_params[1]
            else:
                corr_length = self.background_error_corr_length
            
            correlations = np.exp(-distances_to_obs / corr_length)
            
            # 应用OI修正: analysis = background + correlations^T * weights * innovation
            correction = np.dot(correlations, np.dot(weights, innovation))
            result[i, j] = background_field[i, j] + correction
        
        return result
    
    def _fallback_to_idw(self, station_coords: np.ndarray, station_values: np.ndarray,
                        grid_x: np.ndarray, grid_y: np.ndarray, 
                        mask: np.ndarray) -> np.ndarray:
        """
        降级到IDW方法
        """
        from .idw import IDWInterpolator
        
        idw_config = {
            'method_params': {'IDW': {'power': 2.0}},
            'run_settings': {'cpu_cores': 1},
            'data_processing': {'memory_management': {'use_shared_memory': False}}
        }
        
        idw_interpolator = IDWInterpolator(idw_config)
        return idw_interpolator.interpolate(station_coords, station_values, grid_x, grid_y, mask)
    
    def calculate_cross_validation_error(self, station_coords: np.ndarray, 
                                       station_values: np.ndarray,
                                       target_coords: np.ndarray,
                                       target_values: np.ndarray) -> float:
        """
        计算交叉验证误差（用于参数优化）
        """
        if len(station_coords) < 2:
            return float('inf')
        
        try:
            # 创建临时网格进行插值
            x_min, x_max = np.min(np.concatenate([station_coords[:, 0], target_coords[:, 0]])), \
                          np.max(np.concatenate([station_coords[:, 0], target_coords[:, 0]]))
            y_min, y_max = np.min(np.concatenate([station_coords[:, 1], target_coords[:, 1]])), \
                          np.max(np.concatenate([station_coords[:, 1], target_coords[:, 1]]))
            
            # 创建简单网格
            grid_x, grid_y = np.meshgrid(np.linspace(x_min, x_max, 50),
                                        np.linspace(y_min, y_max, 50))
            mask = np.ones_like(grid_x)
            
            # 执行插值
            interpolated_field = self._perform_oi(station_coords, station_values, 
                                                grid_x, grid_y, mask)
            
            # 在目标点位置提取值
            predicted_values = np.zeros(len(target_coords))
            for i, (target_x, target_y) in enumerate(target_coords):
                distances = np.sqrt((grid_x - target_x)**2 + (grid_y - target_y)**2)
                min_idx = np.unravel_index(np.argmin(distances), distances.shape)
                predicted_values[i] = interpolated_field[min_idx]
            
            # 计算RMSE
            rmse = np.sqrt(np.mean((predicted_values - target_values) ** 2))
            
            return rmse
            
        except Exception as e:
            logger.debug(f"OI交叉验证计算失败: {e}")
            return float('inf')
    
    def get_method_name(self) -> str:
        """获取方法名称"""
        return "OI"
    
    def get_parameters(self) -> Dict:
        """获取当前参数"""
        if self._optimized_params is not None:
            obs_error_var, corr_length = self._optimized_params
        else:
            obs_error_var = self.observation_error_variance
            corr_length = self.background_error_corr_length
        
        return {
            'observation_error_variance': obs_error_var,
            'background_error_corr_length': corr_length
        }
    
    def validate_parameters(self, params: List[float]) -> bool:
        """
        验证参数的有效性
        """
        if len(params) != 2:
            return False
        
        obs_error_var, corr_length = params
        
        # 基本约束
        if obs_error_var <= 0 or corr_length <= 0:
            return False
        
        return True
