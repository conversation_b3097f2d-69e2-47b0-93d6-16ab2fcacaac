# -*- coding: utf-8 -*-
"""
SCE-UA参数优化模块
Shuffled Complex Evolution - University of Arizona
用于自动优化插值方法参数
"""

import numpy as np
from typing import Dict, List, Tuple, Callable, Optional
import logging
import time
from functools import partial

logger = logging.getLogger(__name__)

try:
    from sceua import sceua
    SCEUA_AVAILABLE = True
except ImportError:
    logger.warning("sceua包未安装，将使用简化的优化算法")
    SCEUA_AVAILABLE = False


class SCEUAOptimizer:
    """SCE-UA参数优化器"""
    
    def __init__(self, config: Dict):
        """
        初始化优化器
        
        Args:
            config: 配置字典
        """
        self.config = config
        self.optimization_settings = config['optimization_settings']
        self.sceua_params = self.optimization_settings['sceua_params']
        
        # 优化参数
        self.max_iterations = self.sceua_params['max_iterations']
        self.n_complexes = self.sceua_params['n_complexes']
        self.objective_function_type = self.sceua_params['objective_function']
        self.time_limit = self.sceua_params.get('time_limit', 3600)  # 默认1小时
        
        # 优化边界
        self.optimization_bounds = self.optimization_settings['optimization_bounds']
    
    def optimize_method_parameters(self, method_name: str, interpolator,
                                 station_coords: np.ndarray, station_values: np.ndarray,
                                 target_coords: np.ndarray, target_values: np.ndarray,
                                 grid_x: np.ndarray, grid_y: np.ndarray, 
                                 mask: np.ndarray) -> Tuple[List[float], float]:
        """
        优化指定方法的参数
        
        Args:
            method_name: 方法名称 ('Kriging', 'OI', 'PRISM')
            interpolator: 插值器对象
            station_coords: 插值站点坐标
            station_values: 插值站点值
            target_coords: 目标站点坐标
            target_values: 目标站点真实值
            grid_x: 网格x坐标
            grid_y: 网格y坐标
            mask: 掩膜数组
            
        Returns:
            (最优参数, 最优目标函数值)
        """
        if method_name not in self.optimization_bounds:
            logger.warning(f"方法 {method_name} 没有配置优化边界")
            return [], float('inf')
        
        # 获取参数边界
        bounds = self._get_parameter_bounds(method_name)
        
        if not bounds:
            logger.warning(f"方法 {method_name} 的参数边界无效")
            return [], float('inf')
        
        # 创建目标函数
        objective_func = self._create_objective_function(
            interpolator, station_coords, station_values,
            target_coords, target_values, grid_x, grid_y, mask
        )
        
        logger.info(f"开始优化 {method_name} 参数...")
        start_time = time.time()
        
        try:
            if SCEUA_AVAILABLE:
                optimal_params, optimal_value = self._optimize_with_sceua(objective_func, bounds)
            else:
                optimal_params, optimal_value = self._optimize_with_simple_method(objective_func, bounds)
            
            elapsed_time = time.time() - start_time
            logger.info(f"{method_name} 参数优化完成，耗时 {elapsed_time:.1f}s，最优值: {optimal_value:.4f}")
            
            return optimal_params, optimal_value
            
        except Exception as e:
            logger.error(f"{method_name} 参数优化失败: {e}")
            return [], float('inf')
    
    def _get_parameter_bounds(self, method_name: str) -> List[Tuple[float, float]]:
        """
        获取参数边界
        """
        bounds_config = self.optimization_bounds[method_name]
        bounds = []
        
        if method_name == 'Kriging':
            # [sill, range, nugget]
            variogram_bounds = bounds_config['variogram_parameters']
            for bound in variogram_bounds:
                if len(bound) == 2:
                    bounds.append((bound[0], bound[1]))
                else:
                    logger.error(f"Kriging参数边界格式错误: {bound}")
                    return []
        
        elif method_name == 'OI':
            # [observation_error_variance, background_error_corr_length]
            param_bounds = bounds_config['params']
            for bound in param_bounds:
                if len(bound) == 2:
                    bounds.append((bound[0], bound[1]))
                else:
                    logger.error(f"OI参数边界格式错误: {bound}")
                    return []
        
        elif method_name == 'PRISM':
            # [search_radius, distance_power, elevation_power]
            param_bounds = bounds_config['params']
            for bound in param_bounds:
                if len(bound) == 2:
                    bounds.append((bound[0], bound[1]))
                else:
                    logger.error(f"PRISM参数边界格式错误: {bound}")
                    return []
        
        return bounds
    
    def _create_objective_function(self, interpolator, station_coords: np.ndarray,
                                 station_values: np.ndarray, target_coords: np.ndarray,
                                 target_values: np.ndarray, grid_x: np.ndarray,
                                 grid_y: np.ndarray, mask: np.ndarray) -> Callable:
        """
        创建目标函数
        """
        def objective_function(params: np.ndarray) -> float:
            try:
                # 设置参数到插值器
                interpolator.set_optimized_parameters(params.tolist())
                
                # 计算交叉验证误差
                if hasattr(interpolator, 'calculate_cross_validation_error'):
                    error = interpolator.calculate_cross_validation_error(
                        station_coords, station_values, target_coords, target_values
                    )
                else:
                    # 使用通用的交叉验证方法
                    error = self._generic_cross_validation(
                        interpolator, station_coords, station_values,
                        target_coords, target_values, grid_x, grid_y, mask
                    )
                
                # 根据目标函数类型返回相应值
                if self.objective_function_type == 'rmse':
                    return error
                elif self.objective_function_type == 'nse':
                    # NSE越大越好，所以返回负值进行最小化
                    nse = self._calculate_nse(target_values, target_values - error)
                    return -nse
                else:
                    return error
                    
            except Exception as e:
                logger.debug(f"目标函数计算失败: {e}")
                return float('inf')
        
        return objective_function
    
    def _generic_cross_validation(self, interpolator, station_coords: np.ndarray,
                                station_values: np.ndarray, target_coords: np.ndarray,
                                target_values: np.ndarray, grid_x: np.ndarray,
                                grid_y: np.ndarray, mask: np.ndarray) -> float:
        """
        通用交叉验证方法
        """
        try:
            # 执行插值
            interpolated_field = interpolator.interpolate(
                station_coords, station_values, grid_x, grid_y, mask
            )
            
            # 在目标点位置提取值
            predicted_values = np.zeros(len(target_coords))
            for i, (target_x, target_y) in enumerate(target_coords):
                distances = np.sqrt((grid_x - target_x)**2 + (grid_y - target_y)**2)
                min_idx = np.unravel_index(np.argmin(distances), distances.shape)
                predicted_values[i] = interpolated_field[min_idx]
            
            # 计算RMSE
            rmse = np.sqrt(np.mean((predicted_values - target_values) ** 2))
            return rmse
            
        except Exception as e:
            logger.debug(f"通用交叉验证失败: {e}")
            return float('inf')
    
    def _optimize_with_sceua(self, objective_func: Callable, 
                           bounds: List[Tuple[float, float]]) -> Tuple[List[float], float]:
        """
        使用SCE-UA算法进行优化
        """
        # 转换边界格式
        lower_bounds = np.array([b[0] for b in bounds])
        upper_bounds = np.array([b[1] for b in bounds])
        
        # 设置SCE-UA参数
        sceua_options = {
            'maxn': self.max_iterations,
            'kstop': 10,  # 停止准则
            'pcento': 0.01,  # 收敛准则
            'ngs': self.n_complexes,
        }
        
        # 执行优化
        result = sceua(
            objective_func,
            lower_bounds,
            upper_bounds,
            **sceua_options
        )
        
        optimal_params = result['bestx'].tolist()
        optimal_value = result['bestf']
        
        return optimal_params, optimal_value
    
    def _optimize_with_simple_method(self, objective_func: Callable,
                                   bounds: List[Tuple[float, float]]) -> Tuple[List[float], float]:
        """
        使用简化的随机搜索方法进行优化
        """
        logger.info("使用简化的随机搜索方法进行优化")
        
        n_params = len(bounds)
        n_trials = min(1000, self.max_iterations)
        
        best_params = None
        best_value = float('inf')
        
        start_time = time.time()
        
        for i in range(n_trials):
            # 检查时间限制
            if time.time() - start_time > self.time_limit:
                logger.info(f"达到时间限制，停止优化")
                break
            
            # 随机生成参数
            params = []
            for j in range(n_params):
                lower, upper = bounds[j]
                param = np.random.uniform(lower, upper)
                params.append(param)
            
            # 评估目标函数
            value = objective_func(np.array(params))
            
            # 更新最优解
            if value < best_value:
                best_value = value
                best_params = params.copy()
                logger.debug(f"找到更好的解: {best_value:.4f}")
        
        if best_params is None:
            # 如果没有找到有效解，返回边界中点
            best_params = [(b[0] + b[1]) / 2 for b in bounds]
            best_value = objective_func(np.array(best_params))
        
        return best_params, best_value
    
    def _calculate_nse(self, observed: np.ndarray, predicted: np.ndarray) -> float:
        """
        计算纳什效率系数
        """
        try:
            numerator = np.sum((observed - predicted) ** 2)
            denominator = np.sum((observed - np.mean(observed)) ** 2)
            
            if denominator == 0:
                return 0.0
            
            nse = 1 - (numerator / denominator)
            return nse
            
        except Exception:
            return -float('inf')
    
    def is_optimization_enabled(self, method_name: str) -> bool:
        """
        检查是否启用了指定方法的优化
        """
        if not self.optimization_settings['enable_optimization']:
            return False
        
        methods_to_optimize = self.optimization_settings.get('methods_to_optimize', [])
        return method_name in methods_to_optimize
    
    def get_optimization_summary(self) -> Dict:
        """
        获取优化设置摘要
        """
        return {
            'enabled': self.optimization_settings['enable_optimization'],
            'optimizer': self.optimization_settings['optimizer'],
            'methods_to_optimize': self.optimization_settings.get('methods_to_optimize', []),
            'max_iterations': self.max_iterations,
            'n_complexes': self.n_complexes,
            'objective_function': self.objective_function_type,
            'time_limit': self.time_limit,
            'sceua_available': SCEUA_AVAILABLE
        }
