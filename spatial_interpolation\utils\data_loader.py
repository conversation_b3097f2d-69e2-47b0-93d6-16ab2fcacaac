# -*- coding: utf-8 -*-
"""
数据加载模块
负责加载站点数据、地形数据、降雨数据等
"""

import os
import pandas as pd
import numpy as np
import rasterio
from typing import Dict, List, Tuple, Optional
import logging
from pathlib import Path

logger = logging.getLogger(__name__)


class DataLoader:
    """数据加载器类"""
    
    def __init__(self, config: Dict):
        """
        初始化数据加载器
        
        Args:
            config: 配置字典
        """
        self.config = config
        self.base_dir = Path(config['project_paths']['base_dir'])
        self.terrain_dir = self.base_dir / config['project_paths']['terrain_dir']
        
        # 缓存地形数据
        self._terrain_cache = {}
        self._stations_cache = None
        self._delaunay_cache = None
    
    def load_stations(self) -> pd.DataFrame:
        """
        加载站点信息
        
        Returns:
            包含站点信息的DataFrame
        """
        if self._stations_cache is not None:
            return self._stations_cache
            
        station_file = self.base_dir / self.config['project_paths']['station_file']
        
        if not station_file.exists():
            raise FileNotFoundError(f"站点文件不存在: {station_file}")
        
        try:
            # 读取站点数据，注意中文列名和UTF-8编码
            stations = pd.read_csv(station_file, encoding='utf-8')
            
            # 检查必需的列 - 适配真实数据格式
            required_cols = ['站点', '经度', '纬度', 'NAME']
            missing_cols = [col for col in required_cols if col not in stations.columns]
            if missing_cols:
                raise ValueError(f"站点文件缺少必需的列: {missing_cols}")

            # 确保数据类型正确
            stations['站点'] = stations['站点'].astype(str)
            stations['经度'] = pd.to_numeric(stations['经度'], errors='coerce')
            stations['纬度'] = pd.to_numeric(stations['纬度'], errors='coerce')
            
            # 检查是否有无效坐标
            invalid_coords = stations[stations[['经度', '纬度']].isna().any(axis=1)]
            if not invalid_coords.empty:
                logger.warning(f"发现 {len(invalid_coords)} 个站点坐标无效")
                stations = stations.dropna(subset=['经度', '纬度'])
            
            # 检查重复坐标
            coord_duplicates = stations.duplicated(subset=['经度', '纬度'])
            if coord_duplicates.any():
                logger.warning(f"发现 {coord_duplicates.sum()} 个重复坐标的站点")
            
            self._stations_cache = stations
            logger.info(f"成功加载 {len(stations)} 个站点信息")
            
            return stations
            
        except Exception as e:
            logger.error(f"加载站点文件失败: {e}")
            raise
    
    def load_delaunay_molan(self) -> pd.DataFrame:
        """
        加载Delaunay三角网和莫兰指数分析结果
        
        Returns:
            包含插值站点选择信息的DataFrame
        """
        if self._delaunay_cache is not None:
            return self._delaunay_cache
            
        delaunay_file = self.base_dir / self.config['project_paths']['delaunay_molan_file']
        
        if not delaunay_file.exists():
            raise FileNotFoundError(f"Delaunay_molan文件不存在: {delaunay_file}")
        
        try:
            delaunay_data = pd.read_csv(delaunay_file, encoding='utf-8')
            
            # 检查必需的列
            required_cols = ['target_station', 'significance_level', 'interp_stations_info']
            missing_cols = [col for col in required_cols if col not in delaunay_data.columns]
            if missing_cols:
                raise ValueError(f"Delaunay_molan文件缺少必需的列: {missing_cols}")
            
            # 处理插值站点信息 - 适配真实数据格式
            def parse_interp_stations(stations_str):
                if pd.isna(stations_str):
                    return []
                # 真实数据格式: "茶山(80607800);蒙山(80608500);六樟(80628800);新圩(80630000)"
                stations = []
                for item in str(stations_str).split(';'):
                    if '(' in item and ')' in item:
                        # 提取括号中的站点编号
                        station_id = item.split('(')[1].split(')')[0].strip()
                        if station_id:
                            stations.append(station_id)
                return stations

            delaunay_data['interp_stations_list'] = delaunay_data['interp_stations_info'].apply(parse_interp_stations)
            
            self._delaunay_cache = delaunay_data
            logger.info(f"成功加载 {len(delaunay_data)} 条Delaunay_molan记录")
            
            return delaunay_data
            
        except Exception as e:
            logger.error(f"加载Delaunay_molan文件失败: {e}")
            raise
    
    def load_terrain_data(self) -> Dict[str, Tuple[np.ndarray, Dict]]:
        """
        加载地形数据
        
        Returns:
            包含DEM、坡度、坡向、掩膜数据的字典
        """
        if self._terrain_cache:
            return self._terrain_cache
        
        terrain_files = {
            'dem': 'DEM.asc',
            'slope': 'slope.asc', 
            'aspect': 'aspect.asc',
            'mask': 'mask.asc'
        }
        
        terrain_data = {}
        
        for key, filename in terrain_files.items():
            filepath = self.terrain_dir / filename
            
            if not filepath.exists():
                raise FileNotFoundError(f"地形文件不存在: {filepath}")
            
            try:
                with rasterio.open(filepath) as src:
                    data = src.read(1)  # 读取第一个波段
                    profile = src.profile
                    
                    # 处理无效值
                    nodata = profile.get('nodata', self.config['grid_settings']['nodata_value'])
                    if nodata is not None:
                        data = np.where(data == nodata, np.nan, data)
                    
                    terrain_data[key] = (data, profile)
                    logger.info(f"成功加载地形数据: {filename} ({data.shape})")
                    
            except Exception as e:
                logger.error(f"加载地形文件失败 {filepath}: {e}")
                raise
        
        self._terrain_cache = terrain_data
        return terrain_data
    
    def load_rainfall_data(self, flood_event: str) -> pd.DataFrame:
        """
        加载指定洪水事件的降雨数据
        
        Args:
            flood_event: 洪水事件标识符，如 '2009-1'
            
        Returns:
            包含所有站点时序降雨数据的DataFrame
        """
        input_dir = self.base_dir / self.config['project_paths']['input_dir_template'].format(flood_event=flood_event)
        
        if not input_dir.exists():
            raise FileNotFoundError(f"洪水事件目录不存在: {input_dir}")
        
        # 获取所有CSV文件
        csv_files = list(input_dir.glob('*.csv'))
        if not csv_files:
            raise FileNotFoundError(f"在目录 {input_dir} 中未找到CSV文件")
        
        rainfall_data = {}
        
        for csv_file in csv_files:
            station_id = csv_file.stem  # 文件名作为站点编号
            
            try:
                # 读取单个站点的降雨数据
                df = pd.read_csv(csv_file, encoding='utf-8')
                
                # 检查必需的列（中文列名）
                if '时间' not in df.columns or '雨量' not in df.columns:
                    logger.warning(f"站点 {station_id} 的数据文件缺少必需的列")
                    continue
                
                # 转换时间列
                df['时间'] = pd.to_datetime(df['时间'])
                df['雨量'] = pd.to_numeric(df['雨量'], errors='coerce')
                
                # 去除无效数据
                df = df.dropna(subset=['雨量'])
                
                if len(df) == 0:
                    logger.warning(f"站点 {station_id} 没有有效的降雨数据")
                    continue
                
                # 设置时间为索引
                df = df.set_index('时间')
                rainfall_data[station_id] = df['雨量']
                
            except Exception as e:
                logger.warning(f"加载站点 {station_id} 数据失败: {e}")
                continue
        
        if not rainfall_data:
            raise ValueError(f"未能加载任何有效的降雨数据从 {input_dir}")
        
        # 合并所有站点数据
        combined_data = pd.DataFrame(rainfall_data)
        
        # 填充缺失值为0（假设缺失表示无降雨）
        combined_data = combined_data.fillna(0)
        
        logger.info(f"成功加载洪水事件 {flood_event} 的降雨数据: {len(combined_data.columns)} 个站点, {len(combined_data)} 个时间步")
        
        return combined_data
    
    def get_available_flood_events(self) -> List[str]:
        """
        获取所有可用的洪水事件
        
        Returns:
            洪水事件列表
        """
        input_base = self.base_dir / 'input_another'
        
        if not input_base.exists():
            return []
        
        flood_events = []
        for item in input_base.iterdir():
            if item.is_dir():
                # 检查目录中是否有CSV文件
                csv_files = list(item.glob('*.csv'))
                if csv_files:
                    flood_events.append(item.name)
        
        return sorted(flood_events)
    
    def get_grid_coordinates(self) -> Tuple[np.ndarray, np.ndarray]:
        """
        获取栅格坐标
        
        Returns:
            (x_coords, y_coords) 栅格中心点坐标数组
        """
        terrain_data = self.load_terrain_data()
        mask_data, mask_profile = terrain_data['mask']
        
        # 获取栅格参数
        transform = mask_profile['transform']
        height, width = mask_data.shape
        
        # 计算栅格中心点坐标
        x_coords = np.zeros((height, width))
        y_coords = np.zeros((height, width))
        
        for i in range(height):
            for j in range(width):
                x, y = rasterio.transform.xy(transform, i, j, offset='center')
                x_coords[i, j] = x
                y_coords[i, j] = y
        
        return x_coords, y_coords
