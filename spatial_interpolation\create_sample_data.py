# -*- coding: utf-8 -*-
"""
创建示例数据用于测试系统
"""

import numpy as np
import pandas as pd
import rasterio
from pathlib import Path
from rasterio.transform import from_bounds


def create_sample_data(base_dir: str = "D:/pythondata/jiangyuchazhi/"):
    """创建示例数据"""
    base_path = Path(base_dir)
    
    print("创建示例数据...")
    
    # 1. 创建站点信息
    print("1. 创建站点信息...")
    stations_data = {
        '站点编号': ['3633', '3634', '3635', '3636', '3637', '3638', '3639', '3640'],
        '经度': [113.1, 113.2, 113.3, 113.1, 113.2, 113.3, 113.1, 113.2],
        '纬度': [23.1, 23.1, 23.1, 23.2, 23.2, 23.2, 23.3, 23.3],
        'NAME': ['站点A', '站点B', '站点C', '站点D', '站点E', '站点F', '站点G', '站点H']
    }
    
    stations_df = pd.DataFrame(stations_data)
    stations_df.to_csv(base_path / 'stations.csv', index=False, encoding='utf-8')
    
    # 2. 创建Delaunay_molan数据
    print("2. 创建Delaunay_molan数据...")
    delaunay_data = {
        'target_station': ['3633', '3634', '3635', '3636'],
        'significance_level': [0.05, 0.05, 0.05, 0.05],
        'interp_stations_info': [
            '3634,3635,3637',
            '3633,3635,3638',
            '3633,3634,3639',
            '3637,3638,3640'
        ]
    }
    
    delaunay_df = pd.DataFrame(delaunay_data)
    # 添加解析后的站点列表
    delaunay_df['interp_stations_list'] = delaunay_df['interp_stations_info'].apply(
        lambda x: [s.strip() for s in x.split(',')]
    )
    delaunay_df.to_csv(base_path / 'Delaunay_molan.csv', index=False, encoding='utf-8')
    
    # 3. 创建地形数据
    print("3. 创建地形数据...")
    terrain_dir = base_path / 'terrain' / '90'
    terrain_dir.mkdir(parents=True, exist_ok=True)
    
    # 创建50x50的网格
    width, height = 50, 50
    xmin, ymin, xmax, ymax = 113.0, 23.0, 113.4, 23.4
    
    # 计算变换参数
    transform = from_bounds(xmin, ymin, xmax, ymax, width, height)
    
    # 创建DEM数据（模拟山地地形）
    x = np.linspace(0, 1, width)
    y = np.linspace(0, 1, height)
    X, Y = np.meshgrid(x, y)
    dem_data = 100 + 200 * np.sin(X * np.pi) * np.cos(Y * np.pi) + 50 * np.random.random((height, width))
    
    # 创建坡度数据
    slope_data = np.abs(np.gradient(dem_data)[0]) + np.abs(np.gradient(dem_data)[1])
    slope_data = np.clip(slope_data * 10, 0, 45)  # 限制在0-45度
    
    # 创建坡向数据
    gy, gx = np.gradient(dem_data)
    aspect_data = np.arctan2(gy, gx) * 180 / np.pi
    aspect_data = (aspect_data + 360) % 360  # 转换为0-360度
    
    # 创建掩膜数据
    mask_data = np.ones((height, width))
    
    # 保存地形数据
    profile = {
        'driver': 'AAIGrid',
        'dtype': 'float32',
        'nodata': -9999,
        'width': width,
        'height': height,
        'count': 1,
        'crs': 'EPSG:4326',
        'transform': transform
    }
    
    terrain_files = {
        'DEM.asc': dem_data,
        'slope.asc': slope_data,
        'aspect.asc': aspect_data,
        'mask.asc': mask_data
    }
    
    for filename, data in terrain_files.items():
        filepath = terrain_dir / filename
        with rasterio.open(filepath, 'w', **profile) as dst:
            dst.write(data.astype(np.float32), 1)
    
    # 4. 创建降雨数据
    print("4. 创建降雨数据...")
    input_dir = base_path / 'input_another' / '2009-1'
    input_dir.mkdir(parents=True, exist_ok=True)
    
    # 创建24小时的降雨数据
    time_range = pd.date_range('2009-01-01 00:00:00', periods=24, freq='H')
    
    for station_id in stations_data['站点编号']:
        # 模拟降雨过程：前期无雨，中期有雨，后期减少
        rainfall_pattern = np.zeros(24)
        rainfall_pattern[8:16] = np.random.exponential(2, 8)  # 主要降雨时段
        rainfall_pattern[16:20] = np.random.exponential(1, 4)  # 减弱阶段
        
        # 添加随机变化
        rainfall_pattern += np.random.normal(0, 0.5, 24)
        rainfall_pattern = np.maximum(rainfall_pattern, 0)  # 确保非负
        
        # 创建DataFrame
        rainfall_df = pd.DataFrame({
            '时间': time_range,
            '雨量': rainfall_pattern
        })
        
        # 保存文件
        filepath = input_dir / f'{station_id}.csv'
        rainfall_df.to_csv(filepath, index=False, encoding='utf-8')
    
    # 5. 创建输出目录
    print("5. 创建输出目录...")
    output_dir = base_path / 'output'
    output_dir.mkdir(parents=True, exist_ok=True)
    
    print("示例数据创建完成！")
    print(f"数据位置: {base_path}")
    print("包含:")
    print("  - 8个虚拟雨量站")
    print("  - 24小时降雨数据")
    print("  - 50x50地形栅格")
    print("  - Delaunay三角网配置")


def main():
    """主函数"""
    try:
        create_sample_data()
        print("\n✓ 示例数据创建成功！")
        print("现在可以运行测试: python test_system.py")
        
    except Exception as e:
        print(f"✗ 创建示例数据失败: {e}")
        return 1
    
    return 0


if __name__ == '__main__':
    import sys
    sys.exit(main())
