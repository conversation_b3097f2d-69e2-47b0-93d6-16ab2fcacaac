# -*- coding: utf-8 -*-
"""
反距离权重插值(IDW)方法实现
支持并行计算和共享内存优化
"""

import numpy as np
from scipy.spatial.distance import cdist
from multiprocessing import Pool, RawArray
import multiprocessing as mp
from typing import Tuple, List, Dict, Optional
import logging
from functools import partial

logger = logging.getLogger(__name__)

# 全局变量用于共享内存
_shared_x_coords = None
_shared_y_coords = None
_shared_mask = None
_grid_shape = None


def _init_worker(x_coords_raw, y_coords_raw, mask_raw, shape):
    """
    工作进程初始化函数
    
    Args:
        x_coords_raw: 共享内存中的x坐标数组
        y_coords_raw: 共享内存中的y坐标数组  
        mask_raw: 共享内存中的掩膜数组
        shape: 数组形状
    """
    global _shared_x_coords, _shared_y_coords, _shared_mask, _grid_shape
    
    _grid_shape = shape
    
    # 将共享内存重新构造为numpy数组
    _shared_x_coords = np.frombuffer(x_coords_raw, dtype=np.float64).reshape(shape)
    _shared_y_coords = np.frombuffer(y_coords_raw, dtype=np.float64).reshape(shape)
    _shared_mask = np.frombuffer(mask_raw, dtype=np.float64).reshape(shape)


def _idw_chunk(args):
    """
    处理栅格块的IDW插值
    
    Args:
        args: (row_start, row_end, station_coords, station_values, power)
        
    Returns:
        插值结果数组块
    """
    row_start, row_end, station_coords, station_values, power = args
    
    global _shared_x_coords, _shared_y_coords, _shared_mask
    
    # 获取当前块的坐标
    x_chunk = _shared_x_coords[row_start:row_end, :]
    y_chunk = _shared_y_coords[row_start:row_end, :]
    mask_chunk = _shared_mask[row_start:row_end, :]
    
    chunk_shape = x_chunk.shape
    result_chunk = np.full(chunk_shape, np.nan)
    
    # 展平坐标用于距离计算
    grid_points = np.column_stack([x_chunk.ravel(), y_chunk.ravel()])
    mask_flat = mask_chunk.ravel()
    
    # 只对有效区域进行插值
    valid_indices = ~np.isnan(mask_flat) & (mask_flat != 0)
    
    if not np.any(valid_indices):
        return result_chunk
    
    valid_grid_points = grid_points[valid_indices]
    
    # 计算距离矩阵
    distances = cdist(valid_grid_points, station_coords)
    
    # 避免除零错误
    distances = np.maximum(distances, 1e-10)
    
    # 计算权重
    weights = 1.0 / (distances ** power)
    
    # 归一化权重
    weight_sums = np.sum(weights, axis=1)
    normalized_weights = weights / weight_sums[:, np.newaxis]
    
    # 计算插值结果
    interpolated_values = np.sum(normalized_weights * station_values, axis=1)
    
    # 将结果放回原始形状
    result_flat = np.full(len(mask_flat), np.nan)
    result_flat[valid_indices] = interpolated_values
    result_chunk = result_flat.reshape(chunk_shape)
    
    return result_chunk


class IDWInterpolator:
    """反距离权重插值器"""
    
    def __init__(self, config: Dict):
        """
        初始化IDW插值器
        
        Args:
            config: 配置字典
        """
        self.config = config
        self.power = config['method_params']['IDW']['power']
        self.cpu_cores = config['run_settings']['cpu_cores']
        self.use_shared_memory = config['data_processing']['memory_management']['use_shared_memory']
    
    def interpolate(self, station_coords: np.ndarray, station_values: np.ndarray,
                   grid_x: np.ndarray, grid_y: np.ndarray, 
                   mask: np.ndarray) -> np.ndarray:
        """
        执行IDW插值
        
        Args:
            station_coords: 站点坐标数组 (n_stations, 2)
            station_values: 站点值数组 (n_stations,)
            grid_x: 栅格x坐标
            grid_y: 栅格y坐标
            mask: 掩膜数组
            
        Returns:
            插值结果数组
        """
        if len(station_coords) == 0 or len(station_values) == 0:
            logger.warning("没有有效的站点数据进行插值")
            return np.full_like(grid_x, np.nan)
        
        # 检查是否所有站点值都为零
        if np.all(station_values == 0):
            logger.debug("所有站点值为零，返回零值栅格")
            return np.zeros_like(grid_x)
        
        # 移除无效值
        valid_mask = ~np.isnan(station_values)
        if not np.any(valid_mask):
            logger.warning("所有站点值都是NaN")
            return np.full_like(grid_x, np.nan)
        
        station_coords = station_coords[valid_mask]
        station_values = station_values[valid_mask]
        
        if self.use_shared_memory and self.cpu_cores > 1:
            return self._interpolate_parallel(station_coords, station_values, 
                                            grid_x, grid_y, mask)
        else:
            return self._interpolate_serial(station_coords, station_values, 
                                          grid_x, grid_y, mask)
    
    def _interpolate_serial(self, station_coords: np.ndarray, station_values: np.ndarray,
                          grid_x: np.ndarray, grid_y: np.ndarray, 
                          mask: np.ndarray) -> np.ndarray:
        """
        串行IDW插值
        """
        result = np.full_like(grid_x, np.nan)
        
        # 展平坐标
        grid_points = np.column_stack([grid_x.ravel(), grid_y.ravel()])
        mask_flat = mask.ravel()
        
        # 只对有效区域进行插值
        valid_indices = ~np.isnan(mask_flat) & (mask_flat != 0)
        
        if not np.any(valid_indices):
            return result
        
        valid_grid_points = grid_points[valid_indices]
        
        # 计算距离
        distances = cdist(valid_grid_points, station_coords)
        distances = np.maximum(distances, 1e-10)
        
        # 计算权重和插值
        weights = 1.0 / (distances ** self.power)
        weight_sums = np.sum(weights, axis=1)
        normalized_weights = weights / weight_sums[:, np.newaxis]
        interpolated_values = np.sum(normalized_weights * station_values, axis=1)
        
        # 重新整形
        result_flat = np.full(len(mask_flat), np.nan)
        result_flat[valid_indices] = interpolated_values
        result = result_flat.reshape(grid_x.shape)
        
        return result
    
    def _interpolate_parallel(self, station_coords: np.ndarray, station_values: np.ndarray,
                            grid_x: np.ndarray, grid_y: np.ndarray, 
                            mask: np.ndarray) -> np.ndarray:
        """
        并行IDW插值
        """
        global _shared_x_coords, _shared_y_coords, _shared_mask
        
        grid_shape = grid_x.shape
        
        # 创建共享内存数组
        x_coords_raw = RawArray('d', grid_x.size)
        y_coords_raw = RawArray('d', grid_y.size)
        mask_raw = RawArray('d', mask.size)
        
        # 复制数据到共享内存
        x_coords_shared = np.frombuffer(x_coords_raw, dtype=np.float64).reshape(grid_shape)
        y_coords_shared = np.frombuffer(y_coords_raw, dtype=np.float64).reshape(grid_shape)
        mask_shared = np.frombuffer(mask_raw, dtype=np.float64).reshape(grid_shape)
        
        x_coords_shared[:] = grid_x
        y_coords_shared[:] = grid_y
        mask_shared[:] = mask
        
        # 分割任务
        n_rows = grid_shape[0]
        chunk_size = max(1, n_rows // self.cpu_cores)
        
        tasks = []
        for i in range(0, n_rows, chunk_size):
            row_start = i
            row_end = min(i + chunk_size, n_rows)
            tasks.append((row_start, row_end, station_coords, station_values, self.power))
        
        # 并行处理
        with Pool(processes=self.cpu_cores, 
                 initializer=_init_worker,
                 initargs=(x_coords_raw, y_coords_raw, mask_raw, grid_shape)) as pool:
            
            results = pool.map(_idw_chunk, tasks)
        
        # 合并结果
        final_result = np.full(grid_shape, np.nan)
        
        for i, (row_start, row_end, _, _, _) in enumerate(tasks):
            final_result[row_start:row_end, :] = results[i]
        
        return final_result
    
    def get_method_name(self) -> str:
        """获取方法名称"""
        return "IDW"
    
    def get_parameters(self) -> Dict:
        """获取当前参数"""
        return {
            'power': self.power,
            'cpu_cores': self.cpu_cores
        }
