# -*- coding: utf-8 -*-
"""
错误处理模块
定义自定义异常类和错误处理函数
"""

import logging
from typing import Optional, Any

logger = logging.getLogger(__name__)


class InterpolationError(Exception):
    """插值相关错误的基类"""
    pass


class DataLoadError(InterpolationError):
    """数据加载错误"""
    pass


class ConfigurationError(InterpolationError):
    """配置错误"""
    pass


class OptimizationError(InterpolationError):
    """参数优化错误"""
    pass


class ValidationError(InterpolationError):
    """验证错误"""
    pass


class InsufficientDataError(InterpolationError):
    """数据不足错误"""
    pass


def handle_interpolation_error(func):
    """
    插值方法错误处理装饰器
    """
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except Exception as e:
            logger.error(f"插值方法 {func.__name__} 执行失败: {e}")
            # 返回NaN数组作为失败的标志
            import numpy as np
            if 'grid_x' in kwargs:
                return np.full_like(kwargs['grid_x'], np.nan)
            elif len(args) >= 3:
                return np.full_like(args[2], np.nan)
            else:
                raise InterpolationError(f"插值失败: {e}")
    
    return wrapper


def validate_station_data(stations_data, required_columns=None):
    """
    验证站点数据
    """
    if required_columns is None:
        required_columns = ['站点编号', '经度', '纬度', 'NAME']
    
    if stations_data is None or stations_data.empty:
        raise DataLoadError("站点数据为空")
    
    missing_columns = [col for col in required_columns if col not in stations_data.columns]
    if missing_columns:
        raise DataLoadError(f"站点数据缺少必需的列: {missing_columns}")
    
    # 检查坐标有效性
    invalid_coords = stations_data[
        stations_data[['经度', '纬度']].isna().any(axis=1)
    ]
    
    if not invalid_coords.empty:
        logger.warning(f"发现 {len(invalid_coords)} 个站点坐标无效")


def validate_rainfall_data(rainfall_data, min_stations=2):
    """
    验证降雨数据
    """
    if rainfall_data is None or rainfall_data.empty:
        raise DataLoadError("降雨数据为空")
    
    if len(rainfall_data.columns) < min_stations:
        raise InsufficientDataError(f"站点数量({len(rainfall_data.columns)})少于最小要求({min_stations})")
    
    # 检查时间索引
    if not hasattr(rainfall_data.index, 'to_pydatetime'):
        raise DataLoadError("降雨数据索引不是有效的时间格式")


def validate_terrain_data(terrain_data):
    """
    验证地形数据
    """
    required_keys = ['dem', 'slope', 'aspect', 'mask']
    
    if terrain_data is None:
        raise DataLoadError("地形数据为空")
    
    missing_keys = [key for key in required_keys if key not in terrain_data]
    if missing_keys:
        raise DataLoadError(f"地形数据缺少必需的键: {missing_keys}")
    
    # 检查数据形状一致性
    shapes = [terrain_data[key][0].shape for key in required_keys]
    if not all(shape == shapes[0] for shape in shapes):
        raise DataLoadError("地形数据形状不一致")


def safe_divide(numerator, denominator, default_value=0.0):
    """
    安全除法，避免除零错误
    """
    import numpy as np
    
    if isinstance(denominator, (int, float)):
        return numerator / denominator if denominator != 0 else default_value
    else:
        # 处理数组
        result = np.full_like(numerator, default_value, dtype=float)
        valid_mask = denominator != 0
        result[valid_mask] = numerator[valid_mask] / denominator[valid_mask]
        return result


def check_memory_usage():
    """
    检查内存使用情况
    """
    try:
        import psutil
        memory = psutil.virtual_memory()
        
        if memory.percent > 90:
            logger.warning(f"内存使用率过高: {memory.percent:.1f}%")
            return False
        
        return True
        
    except ImportError:
        # psutil未安装，跳过检查
        return True


def log_system_info():
    """
    记录系统信息
    """
    import platform
    import sys
    
    logger.info("系统信息:")
    logger.info(f"  Python版本: {sys.version}")
    logger.info(f"  操作系统: {platform.system()} {platform.release()}")
    logger.info(f"  处理器: {platform.processor()}")
    
    try:
        import psutil
        memory = psutil.virtual_memory()
        logger.info(f"  总内存: {memory.total / (1024**3):.1f} GB")
        logger.info(f"  可用内存: {memory.available / (1024**3):.1f} GB")
        logger.info(f"  CPU核心数: {psutil.cpu_count()}")
    except ImportError:
        logger.info("  内存和CPU信息: 无法获取（psutil未安装）")


def create_error_report(error: Exception, context: dict = None) -> dict:
    """
    创建错误报告
    """
    import traceback
    import datetime
    
    report = {
        'timestamp': datetime.datetime.now().isoformat(),
        'error_type': type(error).__name__,
        'error_message': str(error),
        'traceback': traceback.format_exc(),
        'context': context or {}
    }
    
    return report


def save_error_report(report: dict, output_dir: str = '.'):
    """
    保存错误报告到文件
    """
    import json
    from pathlib import Path
    
    output_path = Path(output_dir) / f"error_report_{report['timestamp'].replace(':', '-')}.json"
    
    try:
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        logger.info(f"错误报告已保存到: {output_path}")
        
    except Exception as e:
        logger.error(f"保存错误报告失败: {e}")


class ProgressTracker:
    """
    进度跟踪器
    """
    
    def __init__(self, total_tasks: int, description: str = "处理中"):
        self.total_tasks = total_tasks
        self.completed_tasks = 0
        self.description = description
        self.start_time = None
        
        try:
            from tqdm import tqdm
            self.pbar = tqdm(total=total_tasks, desc=description)
            self.use_tqdm = True
        except ImportError:
            self.pbar = None
            self.use_tqdm = False
            logger.info(f"开始{description}: 总任务数 {total_tasks}")
    
    def update(self, increment: int = 1, description: str = None):
        """更新进度"""
        import time
        
        if self.start_time is None:
            self.start_time = time.time()
        
        self.completed_tasks += increment
        
        if self.use_tqdm and self.pbar:
            if description:
                self.pbar.set_description(description)
            self.pbar.update(increment)
        else:
            # 简单的文本进度显示
            progress = self.completed_tasks / self.total_tasks * 100
            elapsed_time = time.time() - self.start_time
            
            if self.completed_tasks > 0:
                eta = elapsed_time * (self.total_tasks - self.completed_tasks) / self.completed_tasks
                logger.info(f"{self.description}: {self.completed_tasks}/{self.total_tasks} "
                           f"({progress:.1f}%) - 预计剩余时间: {eta:.1f}s")
    
    def close(self):
        """关闭进度条"""
        if self.use_tqdm and self.pbar:
            self.pbar.close()
        else:
            logger.info(f"{self.description}完成: {self.completed_tasks}/{self.total_tasks}")


def cleanup_memory():
    """
    清理内存
    """
    import gc
    
    # 强制垃圾回收
    collected = gc.collect()
    
    if collected > 0:
        logger.debug(f"清理了 {collected} 个对象")
    
    return collected
