# -*- coding: utf-8 -*-
"""
模型评估模块
实现留一法交叉验证和各种评估指标计算
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional
import logging
from pathlib import Path

logger = logging.getLogger(__name__)


class ModelValidator:
    """模型验证器"""
    
    def __init__(self, config: Dict):
        """
        初始化验证器
        
        Args:
            config: 配置字典
        """
        self.config = config
    
    def perform_loocv(self, interpolator, flood_event: str, 
                     delaunay_data: pd.DataFrame, stations_data: pd.DataFrame,
                     rainfall_data: pd.DataFrame, grid_x: np.ndarray, 
                     grid_y: np.ndarray, mask: np.ndarray,
                     terrain_data: Optional[Dict] = None) -> Dict:
        """
        执行留一法交叉验证
        
        Args:
            interpolator: 插值器对象
            flood_event: 洪水事件标识
            delaunay_data: Delaunay三角网数据
            stations_data: 站点信息数据
            rainfall_data: 降雨数据
            grid_x: 网格x坐标
            grid_y: 网格y坐标
            mask: 掩膜数组
            terrain_data: 地形数据（PRISM需要）
            
        Returns:
            验证结果字典
        """
        logger.info(f"开始对 {interpolator.get_method_name()} 方法进行留一法交叉验证")
        
        # 过滤当前洪水事件的数据
        # 注意：如果Delaunay_molan.csv中没有flood_event列，则使用所有数据
        if 'flood_event' in delaunay_data.columns:
            event_delaunay = delaunay_data[delaunay_data['flood_event'] == flood_event]
        else:
            event_delaunay = delaunay_data
        
        if event_delaunay.empty:
            logger.warning(f"洪水事件 {flood_event} 没有Delaunay数据")
            return self._create_empty_result()
        
        # 获取目标站点列表
        target_stations = event_delaunay['target_station'].unique()
        
        all_predictions = []
        all_observations = []
        validation_details = []
        
        # 设置地形数据（如果是PRISM方法）
        if hasattr(interpolator, 'set_terrain_data') and terrain_data is not None:
            interpolator.set_terrain_data(
                terrain_data['dem'][0], terrain_data['slope'][0], 
                terrain_data['aspect'][0], grid_x, grid_y
            )
        
        # 对每个目标站点进行验证
        for target_station in target_stations:
            try:
                station_result = self._validate_single_station(
                    interpolator, target_station, event_delaunay, 
                    stations_data, rainfall_data, grid_x, grid_y, mask
                )
                
                if station_result['valid']:
                    all_predictions.extend(station_result['predictions'])
                    all_observations.extend(station_result['observations'])
                    validation_details.append(station_result)
                
            except Exception as e:
                logger.warning(f"验证站点 {target_station} 失败: {e}")
                continue
        
        if not all_predictions:
            logger.warning("没有有效的验证结果")
            return self._create_empty_result()
        
        # 计算整体评估指标
        overall_metrics = self.calculate_metrics(
            np.array(all_observations), np.array(all_predictions)
        )
        
        result = {
            'method': interpolator.get_method_name(),
            'flood_event': flood_event,
            'n_stations': len(validation_details),
            'n_predictions': len(all_predictions),
            'overall_metrics': overall_metrics,
            'station_details': validation_details,
            'parameters': interpolator.get_parameters()
        }
        
        logger.info(f"交叉验证完成: {len(validation_details)} 个站点, "
                   f"RMSE={overall_metrics['rmse']:.3f}, R2={overall_metrics['r2']:.3f}")
        
        return result
    
    def _validate_single_station(self, interpolator, target_station: str,
                               event_delaunay: pd.DataFrame, stations_data: pd.DataFrame,
                               rainfall_data: pd.DataFrame, grid_x: np.ndarray,
                               grid_y: np.ndarray, mask: np.ndarray) -> Dict:
        """
        验证单个站点
        """
        # 获取目标站点的插值站点信息
        target_rows = event_delaunay[event_delaunay['target_station'] == target_station]
        
        if target_rows.empty:
            return {'valid': False, 'reason': 'No delaunay data'}
        
        # 获取插值站点列表
        interp_stations = []
        for _, row in target_rows.iterrows():
            stations_list = row['interp_stations_list']
            if isinstance(stations_list, list):
                interp_stations.extend(stations_list)
            else:
                # 如果是字符串，尝试解析
                if isinstance(stations_list, str):
                    interp_stations.extend([s.strip() for s in stations_list.split(',') if s.strip()])
        
        interp_stations = list(set(interp_stations))  # 去重
        
        if not interp_stations:
            return {'valid': False, 'reason': 'No interpolation stations'}
        
        # 检查站点是否在降雨数据中
        available_stations = set(rainfall_data.columns)
        valid_interp_stations = [s for s in interp_stations if s in available_stations]
        
        if len(valid_interp_stations) < 2:
            return {'valid': False, 'reason': 'Too few valid interpolation stations'}
        
        # 检查目标站点是否有观测数据
        if target_station not in available_stations:
            return {'valid': False, 'reason': 'Target station not in rainfall data'}
        
        # 获取站点坐标
        interp_coords = self._get_station_coordinates(valid_interp_stations, stations_data)
        target_coord = self._get_station_coordinates([target_station], stations_data)
        
        if interp_coords is None or target_coord is None:
            return {'valid': False, 'reason': 'Missing station coordinates'}
        
        # 获取时序数据
        interp_rainfall = rainfall_data[valid_interp_stations]
        target_rainfall = rainfall_data[target_station]
        
        # 执行时序验证
        predictions = []
        observations = []
        
        for timestamp in rainfall_data.index:
            try:
                # 获取当前时刻的数据
                interp_values = interp_rainfall.loc[timestamp].values
                target_value = target_rainfall.loc[timestamp]
                
                # 跳过无效数据
                if np.isnan(target_value) or np.any(np.isnan(interp_values)):
                    continue
                
                # 检查是否为全零情况
                if np.all(interp_values == 0):
                    predictions.append(0.0)
                    observations.append(target_value)
                    continue
                
                # 执行插值
                interpolated_field = interpolator.interpolate(
                    interp_coords, interp_values, grid_x, grid_y, mask
                )
                
                # 在目标站点位置提取值
                target_x, target_y = target_coord[0]
                distances = np.sqrt((grid_x - target_x)**2 + (grid_y - target_y)**2)
                min_idx = np.unravel_index(np.argmin(distances), distances.shape)
                predicted_value = interpolated_field[min_idx]
                
                if not np.isnan(predicted_value):
                    predictions.append(predicted_value)
                    observations.append(target_value)
                
            except Exception as e:
                logger.debug(f"时间步 {timestamp} 插值失败: {e}")
                continue
        
        if len(predictions) < 10:  # 至少需要10个有效预测
            return {'valid': False, 'reason': 'Too few valid predictions'}
        
        # 计算站点级别的指标
        station_metrics = self.calculate_metrics(
            np.array(observations), np.array(predictions)
        )
        
        return {
            'valid': True,
            'station_id': target_station,
            'n_interp_stations': len(valid_interp_stations),
            'n_predictions': len(predictions),
            'predictions': predictions,
            'observations': observations,
            'metrics': station_metrics
        }
    
    def _get_station_coordinates(self, station_ids: List[str], 
                               stations_data: pd.DataFrame) -> Optional[np.ndarray]:
        """
        获取站点坐标
        """
        coords = []
        
        for station_id in station_ids:
            station_row = stations_data[stations_data['站点编号'] == station_id]
            
            if station_row.empty:
                logger.warning(f"站点 {station_id} 坐标信息缺失")
                continue
            
            lon = station_row.iloc[0]['经度']
            lat = station_row.iloc[0]['纬度']
            
            if np.isnan(lon) or np.isnan(lat):
                logger.warning(f"站点 {station_id} 坐标无效")
                continue
            
            coords.append([lon, lat])
        
        if not coords:
            return None
        
        return np.array(coords)
    
    def calculate_metrics(self, observed: np.ndarray, predicted: np.ndarray) -> Dict[str, float]:
        """
        计算评估指标
        
        Args:
            observed: 观测值数组
            predicted: 预测值数组
            
        Returns:
            评估指标字典
        """
        if len(observed) == 0 or len(predicted) == 0:
            return self._create_empty_metrics()
        
        # 移除无效值
        valid_mask = ~(np.isnan(observed) | np.isnan(predicted) | 
                      np.isinf(observed) | np.isinf(predicted))
        
        if not np.any(valid_mask):
            return self._create_empty_metrics()
        
        obs = observed[valid_mask]
        pred = predicted[valid_mask]
        
        if len(obs) == 0:
            return self._create_empty_metrics()
        
        try:
            # 1. 均方根误差 (RMSE)
            rmse = np.sqrt(np.mean((pred - obs) ** 2))
            
            # 2. 平均绝对误差 (MAE)
            mae = np.mean(np.abs(pred - obs))
            
            # 3. 决定系数 (R²)
            if np.std(obs) > 1e-10 and np.std(pred) > 1e-10:
                correlation = np.corrcoef(obs, pred)[0, 1]
                r2 = correlation ** 2 if not np.isnan(correlation) else 0.0
            else:
                r2 = 0.0
            
            # 4. 纳什效率系数 (NSE)
            numerator = np.sum((obs - pred) ** 2)
            denominator = np.sum((obs - np.mean(obs)) ** 2)
            
            if denominator > 1e-10:
                nse = 1 - (numerator / denominator)
            else:
                nse = 0.0
            
            # 5. 平均偏差 (Bias)
            bias = np.mean(pred - obs)
            
            # 6. 相对误差
            mean_obs = np.mean(obs)
            relative_rmse = (rmse / mean_obs * 100) if mean_obs > 1e-10 else np.inf
            
            return {
                'rmse': float(rmse),
                'mae': float(mae),
                'r2': float(r2),
                'nse': float(nse),
                'bias': float(bias),
                'relative_rmse': float(relative_rmse),
                'n_samples': int(len(obs))
            }
            
        except Exception as e:
            logger.error(f"计算评估指标失败: {e}")
            return self._create_empty_metrics()
    
    def _create_empty_metrics(self) -> Dict[str, float]:
        """创建空的评估指标"""
        return {
            'rmse': np.nan,
            'mae': np.nan,
            'r2': np.nan,
            'nse': np.nan,
            'bias': np.nan,
            'relative_rmse': np.nan,
            'n_samples': 0
        }
    
    def _create_empty_result(self) -> Dict:
        """创建空的验证结果"""
        return {
            'method': 'Unknown',
            'flood_event': 'Unknown',
            'n_stations': 0,
            'n_predictions': 0,
            'overall_metrics': self._create_empty_metrics(),
            'station_details': [],
            'parameters': {}
        }
    
    def save_validation_results(self, results: List[Dict], output_dir: Path) -> Path:
        """
        保存验证结果到CSV文件
        
        Args:
            results: 验证结果列表
            output_dir: 输出目录
            
        Returns:
            输出文件路径
        """
        if not results:
            logger.warning("没有验证结果可保存")
            return None
        
        # 准备数据
        summary_data = []
        
        for result in results:
            metrics = result['overall_metrics']
            
            summary_data.append({
                'FloodEvent': result['flood_event'],
                'Method': result['method'],
                'N_Stations': result['n_stations'],
                'N_Predictions': result['n_predictions'],
                'RMSE': metrics['rmse'],
                'MAE': metrics['mae'],
                'R2': metrics['r2'],
                'NSE': metrics['nse'],
                'Bias': metrics['bias'],
                'Relative_RMSE': metrics['relative_rmse']
            })
        
        # 创建DataFrame
        summary_df = pd.DataFrame(summary_data)
        
        # 保存文件
        output_dir.mkdir(parents=True, exist_ok=True)
        output_file = output_dir / 'evaluation_summary.csv'
        
        summary_df.to_csv(output_file, index=False, encoding='utf-8')
        
        logger.info(f"验证结果已保存到: {output_file}")
        return output_file
    
    def create_detailed_report(self, results: List[Dict], output_dir: Path) -> Path:
        """
        创建详细的验证报告
        """
        if not results:
            return None
        
        report_data = []
        
        for result in results:
            for station_detail in result['station_details']:
                if station_detail['valid']:
                    metrics = station_detail['metrics']
                    
                    report_data.append({
                        'FloodEvent': result['flood_event'],
                        'Method': result['method'],
                        'StationID': station_detail['station_id'],
                        'N_InterpStations': station_detail['n_interp_stations'],
                        'N_Predictions': station_detail['n_predictions'],
                        'RMSE': metrics['rmse'],
                        'MAE': metrics['mae'],
                        'R2': metrics['r2'],
                        'NSE': metrics['nse'],
                        'Bias': metrics['bias']
                    })
        
        if not report_data:
            return None
        
        # 创建详细报告
        detailed_df = pd.DataFrame(report_data)
        
        output_dir.mkdir(parents=True, exist_ok=True)
        output_file = output_dir / 'detailed_validation_report.csv'
        
        detailed_df.to_csv(output_file, index=False, encoding='utf-8')
        
        logger.info(f"详细验证报告已保存到: {output_file}")
        return output_file
