# -*- coding: utf-8 -*-
"""
系统测试脚本
验证降雨空间插值系统的基本功能
"""

import sys
import logging
import numpy as np
import pandas as pd
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from utils.data_loader import DataLoader
from utils.raster_io import RasterIO
from core.idw import IDWInterpolator
from core.kriging import KrigingInterpolator
from core.oi import OIInterpolator
from core.prism import PRISMInterpolator
from optimization.sceua_optimizer import SCEUAOptimizer
from evaluation.validator import ModelValidator
from utils.error_handler import log_system_info


def create_test_config():
    """创建测试配置"""
    return {
        'project_paths': {
            'base_dir': "D:/pythondata/jiangyuchazhi/",
            'input_dir_template': "input_another/{flood_event}",
            'output_dir_template': "output/{method}/{flood_event}",
            'terrain_dir': "terrain/90/",
            'station_file': "stations.csv",
            'delaunay_molan_file': "Delaunay_molan.csv"
        },
        'run_settings': {
            'methods_to_run': ['all'],
            'floods_to_run': ['all'],
            'cpu_cores': 2,  # 测试时使用较少核心
            'generate_raster_output': False,  # 测试时不生成栅格
            'debug_mode': True
        },
        'grid_settings': {
            'nodata_value': -9999
        },
        'method_params': {
            'IDW': {'power': 2.0},
            'Kriging': {
                'variogram_model': 'spherical',
                'variogram_parameters': [10.0, 100000.0, 1.0],
                'anisotropy_scaling': 1.0,
                'anisotropy_angle': 0.0,
                'pseudo_inv': False
            },
            'OI': {
                'observation_error_variance': 0.5,
                'background_error_corr_length': 50000.0
            },
            'PRISM': {
                'search_radius': 150000.0,
                'min_stations': 3,
                'max_stations': 10,
                'distance_power': 2.0,
                'elevation_power': 1.0
            }
        },
        'optimization_settings': {
            'enable_optimization': False,  # 测试时禁用优化
            'optimizer': 'SCE-UA',
            'methods_to_optimize': ['Kriging'],
            'sceua_params': {
                'max_iterations': 10,
                'n_complexes': 2,
                'objective_function': 'rmse',
                'time_limit': 60
            },
            'optimization_bounds': {
                'Kriging': {
                    'variogram_parameters': [[1.0, 50.0], [10000.0, 200000.0], [0.1, 5.0]]
                }
            }
        },
        'data_processing': {
            'zero_rainfall_threshold': 0.001,
            'skip_all_zero_timesteps': True,
            'memory_management': {
                'use_shared_memory': False,  # 测试时禁用共享内存
                'batch_size': 10
            }
        },
        'output_settings': {
            'raster_format': 'ASC',
            'time_format': '%Y-%m-%dT%H-%M-%S',
            'compress_output': False,
            'evaluation_format': 'CSV'
        }
    }


def test_data_loading():
    """测试数据加载功能"""
    print("=" * 50)
    print("测试数据加载功能")
    print("=" * 50)
    
    config = create_test_config()
    data_loader = DataLoader(config)
    
    try:
        # 测试站点数据加载
        print("1. 测试站点数据加载...")
        stations = data_loader.load_stations()
        print(f"   成功加载 {len(stations)} 个站点")
        
        # 测试Delaunay数据加载
        print("2. 测试Delaunay数据加载...")
        delaunay = data_loader.load_delaunay_molan()
        print(f"   成功加载 {len(delaunay)} 条Delaunay记录")
        
        # 测试地形数据加载
        print("3. 测试地形数据加载...")
        terrain = data_loader.load_terrain_data()
        print(f"   成功加载地形数据: {list(terrain.keys())}")
        
        # 测试可用洪水事件
        print("4. 测试洪水事件检测...")
        flood_events = data_loader.get_available_flood_events()
        print(f"   发现 {len(flood_events)} 个洪水事件: {flood_events[:3]}...")
        
        if flood_events:
            # 测试降雨数据加载
            print("5. 测试降雨数据加载...")
            rainfall = data_loader.load_rainfall_data(flood_events[0])
            print(f"   成功加载降雨数据: {len(rainfall.columns)} 个站点, {len(rainfall)} 个时间步")
        
        print("✓ 数据加载测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 数据加载测试失败: {e}")
        return False


def test_interpolators():
    """测试插值器功能"""
    print("=" * 50)
    print("测试插值器功能")
    print("=" * 50)
    
    config = create_test_config()
    
    # 创建测试数据
    np.random.seed(42)
    station_coords = np.random.rand(5, 2) * 1000  # 5个随机站点
    station_values = np.random.rand(5) * 10  # 随机降雨值
    
    # 创建测试网格
    x = np.linspace(0, 1000, 20)
    y = np.linspace(0, 1000, 20)
    grid_x, grid_y = np.meshgrid(x, y)
    mask = np.ones_like(grid_x)
    
    interpolators = {
        'IDW': IDWInterpolator(config),
        'Kriging': KrigingInterpolator(config),
        'OI': OIInterpolator(config),
        'PRISM': PRISMInterpolator(config)
    }
    
    # 为PRISM设置虚拟地形数据
    dem = np.random.rand(20, 20) * 100
    slope = np.random.rand(20, 20) * 30
    aspect = np.random.rand(20, 20) * 360
    interpolators['PRISM'].set_terrain_data(dem, slope, aspect, grid_x, grid_y)
    
    results = {}
    
    for name, interpolator in interpolators.items():
        try:
            print(f"测试 {name} 插值器...")
            result = interpolator.interpolate(station_coords, station_values, grid_x, grid_y, mask)
            
            if result is not None and not np.all(np.isnan(result)):
                print(f"   ✓ {name} 插值成功，结果形状: {result.shape}")
                results[name] = True
            else:
                print(f"   ✗ {name} 插值返回无效结果")
                results[name] = False
                
        except Exception as e:
            print(f"   ✗ {name} 插值失败: {e}")
            results[name] = False
    
    success_count = sum(results.values())
    print(f"\n插值器测试结果: {success_count}/{len(interpolators)} 个成功")
    
    return success_count == len(interpolators)


def test_optimization():
    """测试参数优化功能"""
    print("=" * 50)
    print("测试参数优化功能")
    print("=" * 50)
    
    config = create_test_config()
    config['optimization_settings']['enable_optimization'] = True
    
    optimizer = SCEUAOptimizer(config)
    
    # 创建测试数据
    np.random.seed(42)
    station_coords = np.random.rand(8, 2) * 1000
    station_values = np.random.rand(8) * 10
    target_coords = np.random.rand(3, 2) * 1000
    target_values = np.random.rand(3) * 10
    
    x = np.linspace(0, 1000, 10)
    y = np.linspace(0, 1000, 10)
    grid_x, grid_y = np.meshgrid(x, y)
    mask = np.ones_like(grid_x)
    
    try:
        print("测试Kriging参数优化...")
        interpolator = KrigingInterpolator(config)
        
        optimal_params, optimal_value = optimizer.optimize_method_parameters(
            'Kriging', interpolator, station_coords, station_values,
            target_coords, target_values, grid_x, grid_y, mask
        )
        
        if optimal_params:
            print(f"   ✓ 优化成功，最优参数: {optimal_params}")
            print(f"   ✓ 最优值: {optimal_value:.4f}")
            return True
        else:
            print("   ✗ 优化失败，未找到有效参数")
            return False
            
    except Exception as e:
        print(f"   ✗ 优化测试失败: {e}")
        return False


def test_evaluation():
    """测试评估功能"""
    print("=" * 50)
    print("测试评估功能")
    print("=" * 50)
    
    validator = ModelValidator({})
    
    # 创建测试数据
    np.random.seed(42)
    observed = np.random.rand(100) * 10
    predicted = observed + np.random.normal(0, 1, 100)  # 添加噪声
    
    try:
        print("测试评估指标计算...")
        metrics = validator.calculate_metrics(observed, predicted)
        
        print(f"   RMSE: {metrics['rmse']:.3f}")
        print(f"   MAE: {metrics['mae']:.3f}")
        print(f"   R²: {metrics['r2']:.3f}")
        print(f"   NSE: {metrics['nse']:.3f}")
        
        # 检查指标是否合理
        if (0 < metrics['rmse'] < 10 and 
            0 < metrics['mae'] < 10 and 
            0 < metrics['r2'] < 1 and
            -1 < metrics['nse'] < 1):
            print("   ✓ 评估指标计算正确")
            return True
        else:
            print("   ✗ 评估指标值异常")
            return False
            
    except Exception as e:
        print(f"   ✗ 评估测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("降雨空间插值系统测试")
    print("=" * 60)
    
    # 设置日志
    logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
    
    # 记录系统信息
    log_system_info()
    
    # 运行测试
    tests = [
        ("数据加载", test_data_loading),
        ("插值器", test_interpolators),
        ("参数优化", test_optimization),
        ("模型评估", test_evaluation)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"测试 {test_name} 时发生异常: {e}")
            results[test_name] = False
        
        print()  # 空行分隔
    
    # 总结结果
    print("=" * 60)
    print("测试结果总结")
    print("=" * 60)
    
    for test_name, success in results.items():
        status = "✓ 通过" if success else "✗ 失败"
        print(f"{test_name:12s}: {status}")
    
    success_count = sum(results.values())
    total_tests = len(results)
    
    print(f"\n总体结果: {success_count}/{total_tests} 个测试通过")
    
    if success_count == total_tests:
        print("🎉 所有测试通过！系统可以正常使用。")
        return 0
    else:
        print("⚠️  部分测试失败，请检查系统配置和依赖。")
        return 1


if __name__ == '__main__':
    sys.exit(main())
