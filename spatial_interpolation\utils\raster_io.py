# -*- coding: utf-8 -*-
"""
栅格I/O模块
处理栅格文件的读写操作，支持ASC格式
"""

import os
import numpy as np
import rasterio
from rasterio.transform import from_bounds
from pathlib import Path
import logging
from typing import Dict, Tuple, Optional, List
import re

logger = logging.getLogger(__name__)


class RasterIO:
    """栅格文件读写类"""
    
    def __init__(self, config: Dict):
        """
        初始化栅格I/O处理器
        
        Args:
            config: 配置字典
        """
        self.config = config
        self.nodata_value = config['grid_settings']['nodata_value']
    
    def read_asc(self, filepath: Path) -> Tuple[np.ndarray, Dict]:
        """
        读取ASC格式栅格文件
        
        Args:
            filepath: 文件路径
            
        Returns:
            (data, profile) 数据数组和元数据
        """
        try:
            with rasterio.open(filepath) as src:
                data = src.read(1)  # 读取第一个波段
                profile = src.profile
                
                # 处理无效值
                nodata = profile.get('nodata', self.nodata_value)
                if nodata is not None:
                    data = np.where(data == nodata, np.nan, data)
                
                logger.debug(f"成功读取栅格文件: {filepath}")
                return data, profile
                
        except Exception as e:
            logger.error(f"读取栅格文件失败 {filepath}: {e}")
            raise
    
    def write_asc(self, data: np.ndarray, filepath: Path, profile: Dict, 
                  compress: bool = False) -> None:
        """
        写入ASC格式栅格文件
        
        Args:
            data: 数据数组
            filepath: 输出文件路径
            profile: 栅格元数据
            compress: 是否压缩
        """
        try:
            # 确保输出目录存在
            filepath.parent.mkdir(parents=True, exist_ok=True)
            
            # 处理NaN值
            output_data = np.where(np.isnan(data), self.nodata_value, data)
            
            # 更新profile
            output_profile = profile.copy()
            output_profile.update({
                'dtype': output_data.dtype,
                'nodata': self.nodata_value,
                'compress': 'lzw' if compress else None
            })
            
            with rasterio.open(filepath, 'w', **output_profile) as dst:
                dst.write(output_data, 1)
            
            logger.debug(f"成功写入栅格文件: {filepath}")
            
        except Exception as e:
            logger.error(f"写入栅格文件失败 {filepath}: {e}")
            raise
    
    def create_output_filename(self, method: str, flood_event: str, 
                             timestamp: str) -> str:
        """
        创建输出文件名
        
        Args:
            method: 插值方法名
            flood_event: 洪水事件
            timestamp: 时间戳
            
        Returns:
            清理后的文件名
        """
        # 清理时间戳中的特殊字符
        clean_timestamp = self._clean_filename(timestamp)
        
        filename = f"rainfall_{method}_{clean_timestamp}.asc"
        return filename
    
    def _clean_filename(self, filename: str) -> str:
        """
        清理文件名中的特殊字符
        
        Args:
            filename: 原始文件名
            
        Returns:
            清理后的文件名
        """
        # 替换Windows不允许的字符
        invalid_chars = r'[<>:"/\\|?*]'
        clean_name = re.sub(invalid_chars, '-', filename)
        
        # 替换空格
        clean_name = clean_name.replace(' ', '_')
        
        return clean_name
    
    def get_raster_profile_from_mask(self, mask_data: np.ndarray, 
                                   mask_profile: Dict) -> Dict:
        """
        从掩膜数据获取栅格配置
        
        Args:
            mask_data: 掩膜数据
            mask_profile: 掩膜元数据
            
        Returns:
            栅格配置字典
        """
        profile = mask_profile.copy()
        profile.update({
            'dtype': 'float32',
            'nodata': self.nodata_value,
            'count': 1
        })
        
        return profile
    
    def apply_mask(self, data: np.ndarray, mask: np.ndarray) -> np.ndarray:
        """
        应用掩膜到数据
        
        Args:
            data: 原始数据
            mask: 掩膜数据
            
        Returns:
            应用掩膜后的数据
        """
        # 假设掩膜中非零值为有效区域
        masked_data = np.where(np.isnan(mask) | (mask == 0), np.nan, data)
        return masked_data
    
    def calculate_raster_statistics(self, data: np.ndarray) -> Dict:
        """
        计算栅格统计信息
        
        Args:
            data: 栅格数据
            
        Returns:
            统计信息字典
        """
        valid_data = data[~np.isnan(data)]
        
        if len(valid_data) == 0:
            return {
                'count': 0,
                'min': np.nan,
                'max': np.nan,
                'mean': np.nan,
                'std': np.nan
            }
        
        stats = {
            'count': len(valid_data),
            'min': float(np.min(valid_data)),
            'max': float(np.max(valid_data)),
            'mean': float(np.mean(valid_data)),
            'std': float(np.std(valid_data))
        }
        
        return stats
    
    def create_grid_coordinates(self, profile: Dict) -> Tuple[np.ndarray, np.ndarray]:
        """
        创建栅格坐标网格
        
        Args:
            profile: 栅格元数据
            
        Returns:
            (x_coords, y_coords) 坐标网格
        """
        transform = profile['transform']
        height = profile['height']
        width = profile['width']
        
        # 创建坐标网格
        cols, rows = np.meshgrid(np.arange(width), np.arange(height))
        
        # 转换为地理坐标
        x_coords, y_coords = rasterio.transform.xy(transform, rows, cols, offset='center')
        
        return np.array(x_coords), np.array(y_coords)
    
    def batch_write_rasters(self, data_dict: Dict[str, np.ndarray], 
                          output_dir: Path, profile: Dict,
                          method: str, flood_event: str) -> List[Path]:
        """
        批量写入栅格文件
        
        Args:
            data_dict: {timestamp: data} 字典
            output_dir: 输出目录
            profile: 栅格元数据
            method: 插值方法
            flood_event: 洪水事件
            
        Returns:
            输出文件路径列表
        """
        output_files = []
        
        for timestamp, data in data_dict.items():
            filename = self.create_output_filename(method, flood_event, timestamp)
            filepath = output_dir / filename
            
            try:
                self.write_asc(data, filepath, profile, 
                             compress=self.config['output_settings'].get('compress_output', False))
                output_files.append(filepath)
                
            except Exception as e:
                logger.error(f"批量写入失败 {filepath}: {e}")
                continue
        
        logger.info(f"批量写入完成: {len(output_files)} 个文件")
        return output_files
