2025-06-26 18:41:54,218 - __main__ - INFO - ============================================================
2025-06-26 18:41:54,219 - __main__ - INFO - 降雨空间插值系统启动
2025-06-26 18:41:54,220 - __main__ - INFO - ============================================================
2025-06-26 18:41:54,220 - __main__ - INFO - 将处理 1 种方法 × 1 个洪水事件
2025-06-26 18:41:54,220 - __main__ - INFO - 任务 1/1: IDW - 2009-1
2025-06-26 18:41:54,220 - __main__ - INFO - 开始运行 IDW 方法处理洪水事件 2009-1
2025-06-26 18:41:54,221 - __main__ - INFO - 加载基础数据...
2025-06-26 18:41:54,227 - utils.data_loader - INFO - 成功加载 34 个站点信息
2025-06-26 18:41:54,229 - utils.data_loader - INFO - 成功加载 4 条Delaunay_molan记录
2025-06-26 18:41:54,230 - rasterio.env - DEBUG - Entering env context: <rasterio.env.Env object at 0x0000018E1FB39A48>
2025-06-26 18:41:54,231 - rasterio.env - DEBUG - Starting outermost env
2025-06-26 18:41:54,231 - rasterio.env - DEBUG - No GDAL environment exists
2025-06-26 18:41:54,231 - rasterio.env - DEBUG - New GDAL environment <rasterio._env.GDALEnv object at 0x0000018E1FB39248> created
2025-06-26 18:41:54,236 - rasterio._env - DEBUG - GDAL_DATA found in environment.
2025-06-26 18:41:54,236 - rasterio._env - DEBUG - PROJ_LIB found in environment.
2025-06-26 18:41:54,237 - rasterio._env - DEBUG - Started GDALEnv: self=<rasterio._env.GDALEnv object at 0x0000018E1FB39248>.
2025-06-26 18:41:54,237 - rasterio.env - DEBUG - Entered env context: <rasterio.env.Env object at 0x0000018E1FB39A48>
2025-06-26 18:41:54,238 - rasterio._base - DEBUG - Sharing flag: 0
2025-06-26 18:41:54,259 - rasterio._base - DEBUG - Nodata success: 1, Nodata value: -9999.000000
2025-06-26 18:41:54,260 - rasterio._base - DEBUG - Dataset <open DatasetReader name='D:/pythondata/jiangyuchazhi/terrain/90/DEM.asc' mode='r'> is started.
2025-06-26 18:41:54,260 - rasterio.env - DEBUG - Exiting env context: <rasterio.env.Env object at 0x0000018E1FB39A48>
2025-06-26 18:41:54,261 - rasterio.env - DEBUG - Cleared existing <rasterio._env.GDALEnv object at 0x0000018E1FB39248> options
2025-06-26 18:41:54,261 - rasterio._env - DEBUG - Stopped GDALEnv <rasterio._env.GDALEnv object at 0x0000018E1FB39248>.
2025-06-26 18:41:54,261 - rasterio.env - DEBUG - Exiting outermost env
2025-06-26 18:41:54,261 - rasterio.env - DEBUG - Exited env context: <rasterio.env.Env object at 0x0000018E1FB39A48>
2025-06-26 18:41:54,262 - rasterio.env - DEBUG - Entering env context: <rasterio.env.Env object at 0x0000018E1F9538C8>
2025-06-26 18:41:54,262 - rasterio.env - DEBUG - Starting outermost env
2025-06-26 18:41:54,263 - rasterio.env - DEBUG - No GDAL environment exists
2025-06-26 18:41:54,263 - rasterio.env - DEBUG - New GDAL environment <rasterio._env.GDALEnv object at 0x0000018E1FB39A48> created
2025-06-26 18:41:54,265 - rasterio._env - DEBUG - GDAL_DATA found in environment.
2025-06-26 18:41:54,266 - rasterio._env - DEBUG - PROJ_LIB found in environment.
2025-06-26 18:41:54,266 - rasterio._env - DEBUG - Started GDALEnv: self=<rasterio._env.GDALEnv object at 0x0000018E1FB39A48>.
2025-06-26 18:41:54,267 - rasterio.env - DEBUG - Entered env context: <rasterio.env.Env object at 0x0000018E1F9538C8>
2025-06-26 18:41:54,267 - rasterio._io - DEBUG - Output nodata value read from file: -9999.0
2025-06-26 18:41:54,268 - rasterio._io - DEBUG - Output nodata values: [-9999.0]
2025-06-26 18:41:54,268 - rasterio._io - DEBUG - all_valid: False
2025-06-26 18:41:54,269 - rasterio._io - DEBUG - mask_flags: ([<MaskFlags.nodata: 8>],)
2025-06-26 18:41:54,269 - rasterio._io - DEBUG - Jump straight to _read()
2025-06-26 18:41:54,270 - rasterio._io - DEBUG - Window: None
2025-06-26 18:41:54,270 - rasterio._io - DEBUG - IO window xoff=0.0 yoff=0.0 width=50.0 height=50.0
2025-06-26 18:41:54,275 - utils.data_loader - INFO - 成功加载地形数据: DEM.asc ((50, 50))
2025-06-26 18:41:54,276 - rasterio.env - DEBUG - Exiting env context: <rasterio.env.Env object at 0x0000018E1F9538C8>
2025-06-26 18:41:54,276 - rasterio.env - DEBUG - Cleared existing <rasterio._env.GDALEnv object at 0x0000018E1FB39A48> options
2025-06-26 18:41:54,276 - rasterio._env - DEBUG - Stopped GDALEnv <rasterio._env.GDALEnv object at 0x0000018E1FB39A48>.
2025-06-26 18:41:54,277 - rasterio.env - DEBUG - Exiting outermost env
2025-06-26 18:41:54,277 - rasterio.env - DEBUG - Exited env context: <rasterio.env.Env object at 0x0000018E1F9538C8>
2025-06-26 18:41:54,278 - rasterio.env - DEBUG - Entering env context: <rasterio.env.Env object at 0x0000018E1FB3F648>
2025-06-26 18:41:54,278 - rasterio.env - DEBUG - Starting outermost env
2025-06-26 18:41:54,279 - rasterio.env - DEBUG - No GDAL environment exists
2025-06-26 18:41:54,279 - rasterio.env - DEBUG - New GDAL environment <rasterio._env.GDALEnv object at 0x0000018E1FB3F708> created
2025-06-26 18:41:54,281 - rasterio._env - DEBUG - GDAL_DATA found in environment.
2025-06-26 18:41:54,281 - rasterio._env - DEBUG - PROJ_LIB found in environment.
2025-06-26 18:41:54,282 - rasterio._env - DEBUG - Started GDALEnv: self=<rasterio._env.GDALEnv object at 0x0000018E1FB3F708>.
2025-06-26 18:41:54,282 - rasterio.env - DEBUG - Entered env context: <rasterio.env.Env object at 0x0000018E1FB3F648>
2025-06-26 18:41:54,282 - rasterio._base - DEBUG - Sharing flag: 0
2025-06-26 18:41:54,284 - rasterio._base - DEBUG - Nodata success: 1, Nodata value: -9999.000000
2025-06-26 18:41:54,285 - rasterio._base - DEBUG - Dataset <open DatasetReader name='D:/pythondata/jiangyuchazhi/terrain/90/slope.asc' mode='r'> is started.
2025-06-26 18:41:54,286 - rasterio.env - DEBUG - Exiting env context: <rasterio.env.Env object at 0x0000018E1FB3F648>
2025-06-26 18:41:54,287 - rasterio.env - DEBUG - Cleared existing <rasterio._env.GDALEnv object at 0x0000018E1FB3F708> options
2025-06-26 18:41:54,287 - rasterio._env - DEBUG - Stopped GDALEnv <rasterio._env.GDALEnv object at 0x0000018E1FB3F708>.
2025-06-26 18:41:54,288 - rasterio.env - DEBUG - Exiting outermost env
2025-06-26 18:41:54,288 - rasterio.env - DEBUG - Exited env context: <rasterio.env.Env object at 0x0000018E1FB3F648>
2025-06-26 18:41:54,289 - rasterio.env - DEBUG - Entering env context: <rasterio.env.Env object at 0x0000018E1FB39A48>
2025-06-26 18:41:54,290 - rasterio.env - DEBUG - Starting outermost env
2025-06-26 18:41:54,290 - rasterio.env - DEBUG - No GDAL environment exists
2025-06-26 18:41:54,291 - rasterio.env - DEBUG - New GDAL environment <rasterio._env.GDALEnv object at 0x0000018E1FB3F648> created
2025-06-26 18:41:54,294 - rasterio._env - DEBUG - GDAL_DATA found in environment.
2025-06-26 18:41:54,295 - rasterio._env - DEBUG - PROJ_LIB found in environment.
2025-06-26 18:41:54,295 - rasterio._env - DEBUG - Started GDALEnv: self=<rasterio._env.GDALEnv object at 0x0000018E1FB3F648>.
2025-06-26 18:41:54,296 - rasterio.env - DEBUG - Entered env context: <rasterio.env.Env object at 0x0000018E1FB39A48>
2025-06-26 18:41:54,296 - rasterio._io - DEBUG - Output nodata value read from file: -9999.0
2025-06-26 18:41:54,296 - rasterio._io - DEBUG - Output nodata values: [-9999.0]
2025-06-26 18:41:54,297 - rasterio._io - DEBUG - all_valid: False
2025-06-26 18:41:54,297 - rasterio._io - DEBUG - mask_flags: ([<MaskFlags.nodata: 8>],)
2025-06-26 18:41:54,297 - rasterio._io - DEBUG - Jump straight to _read()
2025-06-26 18:41:54,298 - rasterio._io - DEBUG - Window: None
2025-06-26 18:41:54,299 - rasterio._io - DEBUG - IO window xoff=0.0 yoff=0.0 width=50.0 height=50.0
2025-06-26 18:41:54,302 - utils.data_loader - INFO - 成功加载地形数据: slope.asc ((50, 50))
2025-06-26 18:41:54,302 - rasterio.env - DEBUG - Exiting env context: <rasterio.env.Env object at 0x0000018E1FB39A48>
2025-06-26 18:41:54,303 - rasterio.env - DEBUG - Cleared existing <rasterio._env.GDALEnv object at 0x0000018E1FB3F648> options
2025-06-26 18:41:54,304 - rasterio._env - DEBUG - Stopped GDALEnv <rasterio._env.GDALEnv object at 0x0000018E1FB3F648>.
2025-06-26 18:41:54,315 - rasterio.env - DEBUG - Exiting outermost env
2025-06-26 18:41:54,315 - rasterio.env - DEBUG - Exited env context: <rasterio.env.Env object at 0x0000018E1FB39A48>
2025-06-26 18:41:54,316 - rasterio.env - DEBUG - Entering env context: <rasterio.env.Env object at 0x0000018E1FB3F648>
2025-06-26 18:41:54,316 - rasterio.env - DEBUG - Starting outermost env
2025-06-26 18:41:54,317 - rasterio.env - DEBUG - No GDAL environment exists
2025-06-26 18:41:54,317 - rasterio.env - DEBUG - New GDAL environment <rasterio._env.GDALEnv object at 0x0000018E1FB43088> created
2025-06-26 18:41:54,319 - rasterio._env - DEBUG - GDAL_DATA found in environment.
2025-06-26 18:41:54,320 - rasterio._env - DEBUG - PROJ_LIB found in environment.
2025-06-26 18:41:54,329 - rasterio._env - DEBUG - Started GDALEnv: self=<rasterio._env.GDALEnv object at 0x0000018E1FB43088>.
2025-06-26 18:41:54,330 - rasterio.env - DEBUG - Entered env context: <rasterio.env.Env object at 0x0000018E1FB3F648>
2025-06-26 18:41:54,330 - rasterio._base - DEBUG - Sharing flag: 0
2025-06-26 18:41:54,331 - rasterio._base - DEBUG - Nodata success: 1, Nodata value: -9999.000000
2025-06-26 18:41:54,333 - rasterio._base - DEBUG - Dataset <open DatasetReader name='D:/pythondata/jiangyuchazhi/terrain/90/aspect.asc' mode='r'> is started.
2025-06-26 18:41:54,334 - rasterio.env - DEBUG - Exiting env context: <rasterio.env.Env object at 0x0000018E1FB3F648>
2025-06-26 18:41:54,345 - rasterio.env - DEBUG - Cleared existing <rasterio._env.GDALEnv object at 0x0000018E1FB43088> options
2025-06-26 18:41:54,345 - rasterio._env - DEBUG - Stopped GDALEnv <rasterio._env.GDALEnv object at 0x0000018E1FB43088>.
2025-06-26 18:41:54,345 - rasterio.env - DEBUG - Exiting outermost env
2025-06-26 18:41:54,346 - rasterio.env - DEBUG - Exited env context: <rasterio.env.Env object at 0x0000018E1FB3F648>
2025-06-26 18:41:54,346 - rasterio.env - DEBUG - Entering env context: <rasterio.env.Env object at 0x0000018E1FB3FBC8>
2025-06-26 18:41:54,346 - rasterio.env - DEBUG - Starting outermost env
2025-06-26 18:41:54,347 - rasterio.env - DEBUG - No GDAL environment exists
2025-06-26 18:41:54,347 - rasterio.env - DEBUG - New GDAL environment <rasterio._env.GDALEnv object at 0x0000018E1FB43448> created
2025-06-26 18:41:54,348 - rasterio._env - DEBUG - GDAL_DATA found in environment.
2025-06-26 18:41:54,349 - rasterio._env - DEBUG - PROJ_LIB found in environment.
2025-06-26 18:41:54,350 - rasterio._env - DEBUG - Started GDALEnv: self=<rasterio._env.GDALEnv object at 0x0000018E1FB43448>.
2025-06-26 18:41:54,351 - rasterio.env - DEBUG - Entered env context: <rasterio.env.Env object at 0x0000018E1FB3FBC8>
2025-06-26 18:41:54,351 - rasterio._io - DEBUG - Output nodata value read from file: -9999.0
2025-06-26 18:41:54,351 - rasterio._io - DEBUG - Output nodata values: [-9999.0]
2025-06-26 18:41:54,352 - rasterio._io - DEBUG - all_valid: False
2025-06-26 18:41:54,352 - rasterio._io - DEBUG - mask_flags: ([<MaskFlags.nodata: 8>],)
2025-06-26 18:41:54,352 - rasterio._io - DEBUG - Jump straight to _read()
2025-06-26 18:41:54,352 - rasterio._io - DEBUG - Window: None
2025-06-26 18:41:54,353 - rasterio._io - DEBUG - IO window xoff=0.0 yoff=0.0 width=50.0 height=50.0
2025-06-26 18:41:54,367 - utils.data_loader - INFO - 成功加载地形数据: aspect.asc ((50, 50))
2025-06-26 18:41:54,378 - rasterio.env - DEBUG - Exiting env context: <rasterio.env.Env object at 0x0000018E1FB3FBC8>
2025-06-26 18:41:54,378 - rasterio.env - DEBUG - Cleared existing <rasterio._env.GDALEnv object at 0x0000018E1FB43448> options
2025-06-26 18:41:54,379 - rasterio._env - DEBUG - Stopped GDALEnv <rasterio._env.GDALEnv object at 0x0000018E1FB43448>.
2025-06-26 18:41:54,379 - rasterio.env - DEBUG - Exiting outermost env
2025-06-26 18:41:54,379 - rasterio.env - DEBUG - Exited env context: <rasterio.env.Env object at 0x0000018E1FB3FBC8>
2025-06-26 18:41:54,380 - rasterio.env - DEBUG - Entering env context: <rasterio.env.Env object at 0x0000018E1FB43448>
2025-06-26 18:41:54,380 - rasterio.env - DEBUG - Starting outermost env
2025-06-26 18:41:54,380 - rasterio.env - DEBUG - No GDAL environment exists
2025-06-26 18:41:54,381 - rasterio.env - DEBUG - New GDAL environment <rasterio._env.GDALEnv object at 0x0000018E1FB43188> created
2025-06-26 18:41:54,384 - rasterio._env - DEBUG - GDAL_DATA found in environment.
2025-06-26 18:41:54,394 - rasterio._env - DEBUG - PROJ_LIB found in environment.
2025-06-26 18:41:54,394 - rasterio._env - DEBUG - Started GDALEnv: self=<rasterio._env.GDALEnv object at 0x0000018E1FB43188>.
2025-06-26 18:41:54,395 - rasterio.env - DEBUG - Entered env context: <rasterio.env.Env object at 0x0000018E1FB43448>
2025-06-26 18:41:54,395 - rasterio._base - DEBUG - Sharing flag: 0
2025-06-26 18:41:54,397 - rasterio._base - DEBUG - Nodata success: 1, Nodata value: -9999.000000
2025-06-26 18:41:54,397 - rasterio._base - DEBUG - Dataset <open DatasetReader name='D:/pythondata/jiangyuchazhi/terrain/90/mask.asc' mode='r'> is started.
2025-06-26 18:41:54,397 - rasterio.env - DEBUG - Exiting env context: <rasterio.env.Env object at 0x0000018E1FB43448>
2025-06-26 18:41:54,398 - rasterio.env - DEBUG - Cleared existing <rasterio._env.GDALEnv object at 0x0000018E1FB43188> options
2025-06-26 18:41:54,398 - rasterio._env - DEBUG - Stopped GDALEnv <rasterio._env.GDALEnv object at 0x0000018E1FB43188>.
2025-06-26 18:41:54,398 - rasterio.env - DEBUG - Exiting outermost env
2025-06-26 18:41:54,398 - rasterio.env - DEBUG - Exited env context: <rasterio.env.Env object at 0x0000018E1FB43448>
2025-06-26 18:41:54,399 - rasterio.env - DEBUG - Entering env context: <rasterio.env.Env object at 0x0000018E1FB430C8>
2025-06-26 18:41:54,400 - rasterio.env - DEBUG - Starting outermost env
2025-06-26 18:41:54,400 - rasterio.env - DEBUG - No GDAL environment exists
2025-06-26 18:41:54,400 - rasterio.env - DEBUG - New GDAL environment <rasterio._env.GDALEnv object at 0x0000018E1FB43188> created
2025-06-26 18:41:54,412 - rasterio._env - DEBUG - GDAL_DATA found in environment.
2025-06-26 18:41:54,412 - rasterio._env - DEBUG - PROJ_LIB found in environment.
2025-06-26 18:41:54,413 - rasterio._env - DEBUG - Started GDALEnv: self=<rasterio._env.GDALEnv object at 0x0000018E1FB43188>.
2025-06-26 18:41:54,413 - rasterio.env - DEBUG - Entered env context: <rasterio.env.Env object at 0x0000018E1FB430C8>
2025-06-26 18:41:54,413 - rasterio._io - DEBUG - Output nodata value read from file: -9999.0
2025-06-26 18:41:54,413 - rasterio._io - DEBUG - Output nodata values: [-9999.0]
2025-06-26 18:41:54,414 - rasterio._io - DEBUG - all_valid: False
2025-06-26 18:41:54,414 - rasterio._io - DEBUG - mask_flags: ([<MaskFlags.nodata: 8>],)
2025-06-26 18:41:54,414 - rasterio._io - DEBUG - Jump straight to _read()
2025-06-26 18:41:54,415 - rasterio._io - DEBUG - Window: None
2025-06-26 18:41:54,416 - rasterio._io - DEBUG - IO window xoff=0.0 yoff=0.0 width=50.0 height=50.0
2025-06-26 18:41:54,428 - utils.data_loader - INFO - 成功加载地形数据: mask.asc ((50, 50))
2025-06-26 18:41:54,429 - rasterio.env - DEBUG - Exiting env context: <rasterio.env.Env object at 0x0000018E1FB430C8>
2025-06-26 18:41:54,429 - rasterio.env - DEBUG - Cleared existing <rasterio._env.GDALEnv object at 0x0000018E1FB43188> options
2025-06-26 18:41:54,429 - rasterio._env - DEBUG - Stopped GDALEnv <rasterio._env.GDALEnv object at 0x0000018E1FB43188>.
2025-06-26 18:41:54,430 - rasterio.env - DEBUG - Exiting outermost env
2025-06-26 18:41:54,430 - rasterio.env - DEBUG - Exited env context: <rasterio.env.Env object at 0x0000018E1FB430C8>
2025-06-26 18:41:54,673 - utils.data_loader - INFO - 成功加载洪水事件 2009-1 的降雨数据: 46 个站点, 408 个时间步
2025-06-26 18:41:54,685 - __main__ - INFO - 执行交叉验证...
2025-06-26 18:41:54,686 - evaluation.validator - INFO - 开始对 IDW 方法进行留一法交叉验证
2025-06-26 18:41:54,691 - evaluation.validator - WARNING - 没有有效的验证结果
2025-06-26 18:41:54,691 - __main__ - INFO - 生成栅格输出...
2025-06-26 18:41:54,692 - __main__ - INFO - 生成 100 个时间步的栅格输出
2025-06-26 18:41:54,694 - __main__ - INFO - 处理时间步 1/100: 2009-01-01 00:00:00
2025-06-26 18:41:54,744 - __main__ - WARNING - 生成时间步 2009-01-01 00:00:00 的栅格失败: boolean index did not match indexed array along dimension 0; dimension is 34 but corresponding boolean dimension is 46
2025-06-26 18:41:54,775 - __main__ - WARNING - 生成时间步 2009-01-01 01:00:00 的栅格失败: boolean index did not match indexed array along dimension 0; dimension is 34 but corresponding boolean dimension is 46
2025-06-26 18:41:54,798 - __main__ - WARNING - 生成时间步 2009-01-01 02:00:00 的栅格失败: boolean index did not match indexed array along dimension 0; dimension is 34 but corresponding boolean dimension is 46
2025-06-26 18:41:54,830 - __main__ - WARNING - 生成时间步 2009-01-01 03:00:00 的栅格失败: boolean index did not match indexed array along dimension 0; dimension is 34 but corresponding boolean dimension is 46
2025-06-26 18:41:54,856 - __main__ - WARNING - 生成时间步 2009-01-01 04:00:00 的栅格失败: boolean index did not match indexed array along dimension 0; dimension is 34 but corresponding boolean dimension is 46
2025-06-26 18:41:54,886 - __main__ - WARNING - 生成时间步 2009-01-01 05:00:00 的栅格失败: boolean index did not match indexed array along dimension 0; dimension is 34 but corresponding boolean dimension is 46
2025-06-26 18:41:54,914 - __main__ - WARNING - 生成时间步 2009-01-01 06:00:00 的栅格失败: boolean index did not match indexed array along dimension 0; dimension is 34 but corresponding boolean dimension is 46
2025-06-26 18:41:54,944 - __main__ - WARNING - 生成时间步 2009-01-01 07:00:00 的栅格失败: boolean index did not match indexed array along dimension 0; dimension is 34 but corresponding boolean dimension is 46
2025-06-26 18:41:54,987 - __main__ - WARNING - 生成时间步 2009-01-01 08:00:00 的栅格失败: boolean index did not match indexed array along dimension 0; dimension is 34 but corresponding boolean dimension is 46
2025-06-26 18:41:55,014 - __main__ - WARNING - 生成时间步 2009-01-01 09:00:00 的栅格失败: boolean index did not match indexed array along dimension 0; dimension is 34 but corresponding boolean dimension is 46
2025-06-26 18:41:55,015 - __main__ - INFO - 处理时间步 11/100: 2009-01-01 10:00:00
2025-06-26 18:41:55,046 - __main__ - WARNING - 生成时间步 2009-01-01 10:00:00 的栅格失败: boolean index did not match indexed array along dimension 0; dimension is 34 but corresponding boolean dimension is 46
2025-06-26 18:41:55,078 - __main__ - WARNING - 生成时间步 2009-01-01 11:00:00 的栅格失败: boolean index did not match indexed array along dimension 0; dimension is 34 but corresponding boolean dimension is 46
2025-06-26 18:41:55,123 - __main__ - WARNING - 生成时间步 2009-01-01 12:00:00 的栅格失败: boolean index did not match indexed array along dimension 0; dimension is 34 but corresponding boolean dimension is 46
2025-06-26 18:41:55,149 - __main__ - WARNING - 生成时间步 2009-01-01 13:00:00 的栅格失败: boolean index did not match indexed array along dimension 0; dimension is 34 but corresponding boolean dimension is 46
2025-06-26 18:41:55,176 - __main__ - WARNING - 生成时间步 2009-01-01 14:00:00 的栅格失败: boolean index did not match indexed array along dimension 0; dimension is 34 but corresponding boolean dimension is 46
2025-06-26 18:41:55,207 - __main__ - WARNING - 生成时间步 2009-01-01 15:00:00 的栅格失败: boolean index did not match indexed array along dimension 0; dimension is 34 but corresponding boolean dimension is 46
2025-06-26 18:41:55,239 - __main__ - WARNING - 生成时间步 2009-01-01 16:00:00 的栅格失败: boolean index did not match indexed array along dimension 0; dimension is 34 but corresponding boolean dimension is 46
2025-06-26 18:41:55,263 - __main__ - WARNING - 生成时间步 2009-01-01 17:00:00 的栅格失败: boolean index did not match indexed array along dimension 0; dimension is 34 but corresponding boolean dimension is 46
2025-06-26 18:41:55,369 - __main__ - WARNING - 生成时间步 2009-01-01 18:00:00 的栅格失败: boolean index did not match indexed array along dimension 0; dimension is 34 but corresponding boolean dimension is 46
2025-06-26 18:41:55,420 - __main__ - WARNING - 生成时间步 2009-01-01 19:00:00 的栅格失败: boolean index did not match indexed array along dimension 0; dimension is 34 but corresponding boolean dimension is 46
2025-06-26 18:41:55,420 - __main__ - INFO - 处理时间步 21/100: 2009-01-01 20:00:00
2025-06-26 18:41:55,443 - __main__ - WARNING - 生成时间步 2009-01-01 20:00:00 的栅格失败: boolean index did not match indexed array along dimension 0; dimension is 34 but corresponding boolean dimension is 46
2025-06-26 18:41:55,479 - __main__ - WARNING - 生成时间步 2009-01-01 21:00:00 的栅格失败: boolean index did not match indexed array along dimension 0; dimension is 34 but corresponding boolean dimension is 46
2025-06-26 18:41:55,512 - __main__ - WARNING - 生成时间步 2009-01-01 22:00:00 的栅格失败: boolean index did not match indexed array along dimension 0; dimension is 34 but corresponding boolean dimension is 46
2025-06-26 18:41:55,572 - __main__ - WARNING - 生成时间步 2009-01-01 23:00:00 的栅格失败: boolean index did not match indexed array along dimension 0; dimension is 34 but corresponding boolean dimension is 46
2025-06-26 18:41:55,612 - core.idw - DEBUG - 所有站点值为零，返回零值栅格
2025-06-26 18:41:55,615 - rasterio.env - DEBUG - Entering env context: <rasterio.env.Env object at 0x0000018E20BCA1C8>
2025-06-26 18:41:55,616 - rasterio.env - DEBUG - Starting outermost env
2025-06-26 18:41:55,617 - rasterio.env - DEBUG - No GDAL environment exists
2025-06-26 18:41:55,617 - rasterio.env - DEBUG - New GDAL environment <rasterio._env.GDALEnv object at 0x0000018E20BCA7C8> created
2025-06-26 18:41:55,620 - rasterio._env - DEBUG - GDAL_DATA found in environment.
2025-06-26 18:41:55,631 - rasterio._env - DEBUG - PROJ_LIB found in environment.
2025-06-26 18:41:55,632 - rasterio._env - DEBUG - Started GDALEnv: self=<rasterio._env.GDALEnv object at 0x0000018E20BCA7C8>.
2025-06-26 18:41:55,633 - rasterio.env - DEBUG - Entered env context: <rasterio.env.Env object at 0x0000018E20BCA1C8>
2025-06-26 18:41:55,633 - rasterio._io - DEBUG - Path: ParsedPath(path='D:/pythondata/jiangyuchazhi/output/IDW/2009-1/rainfall_IDW_2009-04-16T03-00-00.asc', archive=None, scheme=None), mode: w, driver: AAIGrid
2025-06-26 18:41:55,634 - rasterio._base - DEBUG - Nodata success: 1, Nodata value: -9999.000000
2025-06-26 18:41:55,635 - rasterio.env - DEBUG - Exiting env context: <rasterio.env.Env object at 0x0000018E20BCA1C8>
2025-06-26 18:41:55,635 - rasterio.env - DEBUG - Cleared existing <rasterio._env.GDALEnv object at 0x0000018E20BCA7C8> options
2025-06-26 18:41:55,635 - rasterio._env - DEBUG - Stopped GDALEnv <rasterio._env.GDALEnv object at 0x0000018E20BCA7C8>.
2025-06-26 18:41:55,636 - rasterio.env - DEBUG - Exiting outermost env
2025-06-26 18:41:55,636 - rasterio.env - DEBUG - Exited env context: <rasterio.env.Env object at 0x0000018E20BCA1C8>
2025-06-26 18:41:55,637 - rasterio.env - DEBUG - Entering env context: <rasterio.env.Env object at 0x0000018E20BCAEC8>
2025-06-26 18:41:55,637 - rasterio.env - DEBUG - Starting outermost env
2025-06-26 18:41:55,638 - rasterio.env - DEBUG - No GDAL environment exists
2025-06-26 18:41:55,638 - rasterio.env - DEBUG - New GDAL environment <rasterio._env.GDALEnv object at 0x0000018E20BCA1C8> created
2025-06-26 18:41:55,641 - rasterio._env - DEBUG - GDAL_DATA found in environment.
2025-06-26 18:41:55,641 - rasterio._env - DEBUG - PROJ_LIB found in environment.
2025-06-26 18:41:55,642 - rasterio._env - DEBUG - Started GDALEnv: self=<rasterio._env.GDALEnv object at 0x0000018E20BCA1C8>.
2025-06-26 18:41:55,642 - rasterio.env - DEBUG - Entered env context: <rasterio.env.Env object at 0x0000018E20BCAEC8>
2025-06-26 18:41:55,663 - rasterio._io - DEBUG - Skipped delete for overwrite. Dataset does not exist: 'D:/pythondata/jiangyuchazhi/output/IDW/2009-1/rainfall_IDW_2009-04-16T03-00-00.asc'
2025-06-26 18:41:55,663 - rasterio._io - DEBUG - Option: ('TILED', b'False')

2025-06-26 18:41:55,664 - rasterio._io - DEBUG - Option: ('COMPRESS', b'None')

2025-06-26 18:41:55,666 - rasterio._env - WARNING - CPLE_NotSupported in driver AAIGrid does not support creation option TILED
2025-06-26 18:41:55,667 - rasterio._env - WARNING - CPLE_NotSupported in driver AAIGrid does not support creation option COMPRESS
2025-06-26 18:41:55,712 - rasterio._io - DEBUG - Created copy from MEM file: D:/pythondata/jiangyuchazhi/output/IDW/2009-1/rainfall_IDW_2009-04-16T03-00-00.asc
2025-06-26 18:41:55,713 - rasterio.env - DEBUG - Exiting env context: <rasterio.env.Env object at 0x0000018E20BCAEC8>
2025-06-26 18:41:55,713 - rasterio.env - DEBUG - Cleared existing <rasterio._env.GDALEnv object at 0x0000018E20BCA1C8> options
2025-06-26 18:41:55,716 - rasterio._env - DEBUG - Stopped GDALEnv <rasterio._env.GDALEnv object at 0x0000018E20BCA1C8>.
2025-06-26 18:41:55,727 - rasterio.env - DEBUG - Exiting outermost env
2025-06-26 18:41:55,727 - rasterio.env - DEBUG - Exited env context: <rasterio.env.Env object at 0x0000018E20BCAEC8>
2025-06-26 18:41:55,728 - utils.raster_io - DEBUG - 成功写入栅格文件: D:\pythondata\jiangyuchazhi\output\IDW\2009-1\rainfall_IDW_2009-04-16T03-00-00.asc
2025-06-26 18:41:55,766 - core.idw - DEBUG - 所有站点值为零，返回零值栅格
2025-06-26 18:41:55,767 - rasterio.env - DEBUG - Entering env context: <rasterio.env.Env object at 0x0000018E20BC2E08>
2025-06-26 18:41:55,767 - rasterio.env - DEBUG - Starting outermost env
2025-06-26 18:41:55,767 - rasterio.env - DEBUG - No GDAL environment exists
2025-06-26 18:41:55,768 - rasterio.env - DEBUG - New GDAL environment <rasterio._env.GDALEnv object at 0x0000018E20BCB208> created
2025-06-26 18:41:55,769 - rasterio._env - DEBUG - GDAL_DATA found in environment.
2025-06-26 18:41:55,769 - rasterio._env - DEBUG - PROJ_LIB found in environment.
2025-06-26 18:41:55,770 - rasterio._env - DEBUG - Started GDALEnv: self=<rasterio._env.GDALEnv object at 0x0000018E20BCB208>.
2025-06-26 18:41:55,770 - rasterio.env - DEBUG - Entered env context: <rasterio.env.Env object at 0x0000018E20BC2E08>
2025-06-26 18:41:55,770 - rasterio._io - DEBUG - Path: ParsedPath(path='D:/pythondata/jiangyuchazhi/output/IDW/2009-1/rainfall_IDW_2009-04-16T04-00-00.asc', archive=None, scheme=None), mode: w, driver: AAIGrid
2025-06-26 18:41:55,771 - rasterio._base - DEBUG - Nodata success: 1, Nodata value: -9999.000000
2025-06-26 18:41:55,771 - rasterio.env - DEBUG - Exiting env context: <rasterio.env.Env object at 0x0000018E20BC2E08>
2025-06-26 18:41:55,771 - rasterio.env - DEBUG - Cleared existing <rasterio._env.GDALEnv object at 0x0000018E20BCB208> options
2025-06-26 18:41:55,772 - rasterio._env - DEBUG - Stopped GDALEnv <rasterio._env.GDALEnv object at 0x0000018E20BCB208>.
2025-06-26 18:41:55,772 - rasterio.env - DEBUG - Exiting outermost env
2025-06-26 18:41:55,772 - rasterio.env - DEBUG - Exited env context: <rasterio.env.Env object at 0x0000018E20BC2E08>
2025-06-26 18:41:55,773 - rasterio.env - DEBUG - Entering env context: <rasterio.env.Env object at 0x0000018E20BAADC8>
2025-06-26 18:41:55,783 - rasterio.env - DEBUG - Starting outermost env
2025-06-26 18:41:55,784 - rasterio.env - DEBUG - No GDAL environment exists
2025-06-26 18:41:55,784 - rasterio.env - DEBUG - New GDAL environment <rasterio._env.GDALEnv object at 0x0000018E20BC2E08> created
2025-06-26 18:41:55,786 - rasterio._env - DEBUG - GDAL_DATA found in environment.
2025-06-26 18:41:55,786 - rasterio._env - DEBUG - PROJ_LIB found in environment.
2025-06-26 18:41:55,786 - rasterio._env - DEBUG - Started GDALEnv: self=<rasterio._env.GDALEnv object at 0x0000018E20BC2E08>.
2025-06-26 18:41:55,787 - rasterio.env - DEBUG - Entered env context: <rasterio.env.Env object at 0x0000018E20BAADC8>
2025-06-26 18:41:55,787 - rasterio._io - DEBUG - Skipped delete for overwrite. Dataset does not exist: 'D:/pythondata/jiangyuchazhi/output/IDW/2009-1/rainfall_IDW_2009-04-16T04-00-00.asc'
2025-06-26 18:41:55,788 - rasterio._io - DEBUG - Option: ('TILED', b'False')

2025-06-26 18:41:55,788 - rasterio._io - DEBUG - Option: ('COMPRESS', b'None')

2025-06-26 18:41:55,789 - rasterio._env - WARNING - CPLE_NotSupported in driver AAIGrid does not support creation option TILED
2025-06-26 18:41:55,789 - rasterio._env - WARNING - CPLE_NotSupported in driver AAIGrid does not support creation option COMPRESS
2025-06-26 18:41:55,794 - rasterio._io - DEBUG - Created copy from MEM file: D:/pythondata/jiangyuchazhi/output/IDW/2009-1/rainfall_IDW_2009-04-16T04-00-00.asc
2025-06-26 18:41:55,795 - rasterio.env - DEBUG - Exiting env context: <rasterio.env.Env object at 0x0000018E20BAADC8>
2025-06-26 18:41:55,795 - rasterio.env - DEBUG - Cleared existing <rasterio._env.GDALEnv object at 0x0000018E20BC2E08> options
2025-06-26 18:41:55,795 - rasterio._env - DEBUG - Stopped GDALEnv <rasterio._env.GDALEnv object at 0x0000018E20BC2E08>.
2025-06-26 18:41:55,796 - rasterio.env - DEBUG - Exiting outermost env
2025-06-26 18:41:55,796 - rasterio.env - DEBUG - Exited env context: <rasterio.env.Env object at 0x0000018E20BAADC8>
2025-06-26 18:41:55,796 - utils.raster_io - DEBUG - 成功写入栅格文件: D:\pythondata\jiangyuchazhi\output\IDW\2009-1\rainfall_IDW_2009-04-16T04-00-00.asc
2025-06-26 18:41:55,825 - core.idw - DEBUG - 所有站点值为零，返回零值栅格
2025-06-26 18:41:55,827 - rasterio.env - DEBUG - Entering env context: <rasterio.env.Env object at 0x0000018E20BCA7C8>
2025-06-26 18:41:55,828 - rasterio.env - DEBUG - Starting outermost env
2025-06-26 18:41:55,828 - rasterio.env - DEBUG - No GDAL environment exists
2025-06-26 18:41:55,829 - rasterio.env - DEBUG - New GDAL environment <rasterio._env.GDALEnv object at 0x0000018E20B9DC88> created
2025-06-26 18:41:55,831 - rasterio._env - DEBUG - GDAL_DATA found in environment.
2025-06-26 18:41:55,831 - rasterio._env - DEBUG - PROJ_LIB found in environment.
2025-06-26 18:41:55,832 - rasterio._env - DEBUG - Started GDALEnv: self=<rasterio._env.GDALEnv object at 0x0000018E20B9DC88>.
2025-06-26 18:41:55,832 - rasterio.env - DEBUG - Entered env context: <rasterio.env.Env object at 0x0000018E20BCA7C8>
2025-06-26 18:41:55,833 - rasterio._io - DEBUG - Path: ParsedPath(path='D:/pythondata/jiangyuchazhi/output/IDW/2009-1/rainfall_IDW_2009-04-16T05-00-00.asc', archive=None, scheme=None), mode: w, driver: AAIGrid
2025-06-26 18:41:55,845 - rasterio._base - DEBUG - Nodata success: 1, Nodata value: -9999.000000
2025-06-26 18:41:55,846 - rasterio.env - DEBUG - Exiting env context: <rasterio.env.Env object at 0x0000018E20BCA7C8>
2025-06-26 18:41:55,846 - rasterio.env - DEBUG - Cleared existing <rasterio._env.GDALEnv object at 0x0000018E20B9DC88> options
2025-06-26 18:41:55,846 - rasterio._env - DEBUG - Stopped GDALEnv <rasterio._env.GDALEnv object at 0x0000018E20B9DC88>.
2025-06-26 18:41:55,846 - rasterio.env - DEBUG - Exiting outermost env
2025-06-26 18:41:55,847 - rasterio.env - DEBUG - Exited env context: <rasterio.env.Env object at 0x0000018E20BCA7C8>
2025-06-26 18:41:55,847 - rasterio.env - DEBUG - Entering env context: <rasterio.env.Env object at 0x0000018E20BC9708>
2025-06-26 18:41:55,847 - rasterio.env - DEBUG - Starting outermost env
2025-06-26 18:41:55,847 - rasterio.env - DEBUG - No GDAL environment exists
2025-06-26 18:41:55,847 - rasterio.env - DEBUG - New GDAL environment <rasterio._env.GDALEnv object at 0x0000018E20BCA7C8> created
2025-06-26 18:41:55,850 - rasterio._env - DEBUG - GDAL_DATA found in environment.
2025-06-26 18:41:55,852 - rasterio._env - DEBUG - PROJ_LIB found in environment.
2025-06-26 18:41:55,853 - rasterio._env - DEBUG - Started GDALEnv: self=<rasterio._env.GDALEnv object at 0x0000018E20BCA7C8>.
2025-06-26 18:41:55,854 - rasterio.env - DEBUG - Entered env context: <rasterio.env.Env object at 0x0000018E20BC9708>
2025-06-26 18:41:55,860 - rasterio._io - DEBUG - Skipped delete for overwrite. Dataset does not exist: 'D:/pythondata/jiangyuchazhi/output/IDW/2009-1/rainfall_IDW_2009-04-16T05-00-00.asc'
2025-06-26 18:41:55,860 - rasterio._io - DEBUG - Option: ('TILED', b'False')

2025-06-26 18:41:55,861 - rasterio._io - DEBUG - Option: ('COMPRESS', b'None')

2025-06-26 18:41:55,861 - rasterio._env - WARNING - CPLE_NotSupported in driver AAIGrid does not support creation option TILED
2025-06-26 18:41:55,862 - rasterio._env - WARNING - CPLE_NotSupported in driver AAIGrid does not support creation option COMPRESS
2025-06-26 18:41:55,868 - rasterio._io - DEBUG - Created copy from MEM file: D:/pythondata/jiangyuchazhi/output/IDW/2009-1/rainfall_IDW_2009-04-16T05-00-00.asc
2025-06-26 18:41:55,869 - rasterio.env - DEBUG - Exiting env context: <rasterio.env.Env object at 0x0000018E20BC9708>
2025-06-26 18:41:55,872 - rasterio.env - DEBUG - Cleared existing <rasterio._env.GDALEnv object at 0x0000018E20BCA7C8> options
2025-06-26 18:41:55,872 - rasterio._env - DEBUG - Stopped GDALEnv <rasterio._env.GDALEnv object at 0x0000018E20BCA7C8>.
2025-06-26 18:41:55,873 - rasterio.env - DEBUG - Exiting outermost env
2025-06-26 18:41:55,873 - rasterio.env - DEBUG - Exited env context: <rasterio.env.Env object at 0x0000018E20BC9708>
2025-06-26 18:41:55,873 - utils.raster_io - DEBUG - 成功写入栅格文件: D:\pythondata\jiangyuchazhi\output\IDW\2009-1\rainfall_IDW_2009-04-16T05-00-00.asc
2025-06-26 18:41:55,905 - __main__ - WARNING - 生成时间步 2009-04-16 06:00:00 的栅格失败: boolean index did not match indexed array along dimension 0; dimension is 34 but corresponding boolean dimension is 46
2025-06-26 18:41:55,939 - __main__ - WARNING - 生成时间步 2009-04-16 07:00:00 的栅格失败: boolean index did not match indexed array along dimension 0; dimension is 34 but corresponding boolean dimension is 46
2025-06-26 18:41:55,965 - core.idw - DEBUG - 所有站点值为零，返回零值栅格
2025-06-26 18:41:55,966 - rasterio.env - DEBUG - Entering env context: <rasterio.env.Env object at 0x0000018E20BCB208>
2025-06-26 18:41:55,966 - rasterio.env - DEBUG - Starting outermost env
2025-06-26 18:41:55,966 - rasterio.env - DEBUG - No GDAL environment exists
2025-06-26 18:41:55,967 - rasterio.env - DEBUG - New GDAL environment <rasterio._env.GDALEnv object at 0x0000018E20BCAEC8> created
2025-06-26 18:41:55,971 - rasterio._env - DEBUG - GDAL_DATA found in environment.
2025-06-26 18:41:55,971 - rasterio._env - DEBUG - PROJ_LIB found in environment.
2025-06-26 18:41:55,973 - rasterio._env - DEBUG - Started GDALEnv: self=<rasterio._env.GDALEnv object at 0x0000018E20BCAEC8>.
2025-06-26 18:41:55,973 - rasterio.env - DEBUG - Entered env context: <rasterio.env.Env object at 0x0000018E20BCB208>
2025-06-26 18:41:55,974 - rasterio._io - DEBUG - Path: ParsedPath(path='D:/pythondata/jiangyuchazhi/output/IDW/2009-1/rainfall_IDW_2009-04-16T08-00-00.asc', archive=None, scheme=None), mode: w, driver: AAIGrid
2025-06-26 18:41:55,975 - rasterio._base - DEBUG - Nodata success: 1, Nodata value: -9999.000000
2025-06-26 18:41:55,975 - rasterio.env - DEBUG - Exiting env context: <rasterio.env.Env object at 0x0000018E20BCB208>
2025-06-26 18:41:55,976 - rasterio.env - DEBUG - Cleared existing <rasterio._env.GDALEnv object at 0x0000018E20BCAEC8> options
2025-06-26 18:41:55,976 - rasterio._env - DEBUG - Stopped GDALEnv <rasterio._env.GDALEnv object at 0x0000018E20BCAEC8>.
2025-06-26 18:41:55,977 - rasterio.env - DEBUG - Exiting outermost env
2025-06-26 18:41:55,979 - rasterio.env - DEBUG - Exited env context: <rasterio.env.Env object at 0x0000018E20BCB208>
2025-06-26 18:41:55,979 - rasterio.env - DEBUG - Entering env context: <rasterio.env.Env object at 0x0000018E20BCB5C8>
2025-06-26 18:41:55,979 - rasterio.env - DEBUG - Starting outermost env
2025-06-26 18:41:55,980 - rasterio.env - DEBUG - No GDAL environment exists
2025-06-26 18:41:55,980 - rasterio.env - DEBUG - New GDAL environment <rasterio._env.GDALEnv object at 0x0000018E20BCB208> created
2025-06-26 18:41:55,981 - rasterio._env - DEBUG - GDAL_DATA found in environment.
2025-06-26 18:41:55,981 - rasterio._env - DEBUG - PROJ_LIB found in environment.
2025-06-26 18:41:55,982 - rasterio._env - DEBUG - Started GDALEnv: self=<rasterio._env.GDALEnv object at 0x0000018E20BCB208>.
2025-06-26 18:41:55,982 - rasterio.env - DEBUG - Entered env context: <rasterio.env.Env object at 0x0000018E20BCB5C8>
2025-06-26 18:41:55,984 - rasterio._io - DEBUG - Skipped delete for overwrite. Dataset does not exist: 'D:/pythondata/jiangyuchazhi/output/IDW/2009-1/rainfall_IDW_2009-04-16T08-00-00.asc'
2025-06-26 18:41:55,993 - rasterio._io - DEBUG - Option: ('TILED', b'False')

2025-06-26 18:41:55,994 - rasterio._io - DEBUG - Option: ('COMPRESS', b'None')

2025-06-26 18:41:55,995 - rasterio._env - WARNING - CPLE_NotSupported in driver AAIGrid does not support creation option TILED
2025-06-26 18:41:55,995 - rasterio._env - WARNING - CPLE_NotSupported in driver AAIGrid does not support creation option COMPRESS
2025-06-26 18:41:56,001 - rasterio._io - DEBUG - Created copy from MEM file: D:/pythondata/jiangyuchazhi/output/IDW/2009-1/rainfall_IDW_2009-04-16T08-00-00.asc
2025-06-26 18:41:56,002 - rasterio.env - DEBUG - Exiting env context: <rasterio.env.Env object at 0x0000018E20BCB5C8>
2025-06-26 18:41:56,004 - rasterio.env - DEBUG - Cleared existing <rasterio._env.GDALEnv object at 0x0000018E20BCB208> options
2025-06-26 18:41:56,005 - rasterio._env - DEBUG - Stopped GDALEnv <rasterio._env.GDALEnv object at 0x0000018E20BCB208>.
2025-06-26 18:41:56,006 - rasterio.env - DEBUG - Exiting outermost env
2025-06-26 18:41:56,006 - rasterio.env - DEBUG - Exited env context: <rasterio.env.Env object at 0x0000018E20BCB5C8>
2025-06-26 18:41:56,007 - utils.raster_io - DEBUG - 成功写入栅格文件: D:\pythondata\jiangyuchazhi\output\IDW\2009-1\rainfall_IDW_2009-04-16T08-00-00.asc
2025-06-26 18:41:56,008 - __main__ - INFO - 处理时间步 31/100: 2009-04-16 09:00:00
2025-06-26 18:41:56,044 - __main__ - WARNING - 生成时间步 2009-04-16 09:00:00 的栅格失败: boolean index did not match indexed array along dimension 0; dimension is 34 but corresponding boolean dimension is 46
2025-06-26 18:41:56,067 - __main__ - WARNING - 生成时间步 2009-04-16 10:00:00 的栅格失败: boolean index did not match indexed array along dimension 0; dimension is 34 but corresponding boolean dimension is 46
2025-06-26 18:41:56,100 - __main__ - WARNING - 生成时间步 2009-04-16 11:00:00 的栅格失败: boolean index did not match indexed array along dimension 0; dimension is 34 but corresponding boolean dimension is 46
2025-06-26 18:41:56,137 - __main__ - WARNING - 生成时间步 2009-04-16 12:00:00 的栅格失败: boolean index did not match indexed array along dimension 0; dimension is 34 but corresponding boolean dimension is 46
2025-06-26 18:41:56,164 - __main__ - WARNING - 生成时间步 2009-04-16 13:00:00 的栅格失败: boolean index did not match indexed array along dimension 0; dimension is 34 but corresponding boolean dimension is 46
2025-06-26 18:41:56,185 - __main__ - WARNING - 生成时间步 2009-04-16 14:00:00 的栅格失败: boolean index did not match indexed array along dimension 0; dimension is 34 but corresponding boolean dimension is 46
2025-06-26 18:41:56,211 - __main__ - WARNING - 生成时间步 2009-04-16 15:00:00 的栅格失败: boolean index did not match indexed array along dimension 0; dimension is 34 but corresponding boolean dimension is 46
2025-06-26 18:41:56,243 - __main__ - WARNING - 生成时间步 2009-04-16 16:00:00 的栅格失败: boolean index did not match indexed array along dimension 0; dimension is 34 but corresponding boolean dimension is 46
2025-06-26 18:41:56,270 - core.idw - DEBUG - 所有站点值为零，返回零值栅格
2025-06-26 18:41:56,272 - rasterio.env - DEBUG - Entering env context: <rasterio.env.Env object at 0x0000018E20BCA7C8>
2025-06-26 18:41:56,272 - rasterio.env - DEBUG - Starting outermost env
2025-06-26 18:41:56,273 - rasterio.env - DEBUG - No GDAL environment exists
2025-06-26 18:41:56,273 - rasterio.env - DEBUG - New GDAL environment <rasterio._env.GDALEnv object at 0x0000018E20BAA988> created
2025-06-26 18:41:56,274 - rasterio._env - DEBUG - GDAL_DATA found in environment.
2025-06-26 18:41:56,274 - rasterio._env - DEBUG - PROJ_LIB found in environment.
2025-06-26 18:41:56,275 - rasterio._env - DEBUG - Started GDALEnv: self=<rasterio._env.GDALEnv object at 0x0000018E20BAA988>.
2025-06-26 18:41:56,275 - rasterio.env - DEBUG - Entered env context: <rasterio.env.Env object at 0x0000018E20BCA7C8>
2025-06-26 18:41:56,275 - rasterio._io - DEBUG - Path: ParsedPath(path='D:/pythondata/jiangyuchazhi/output/IDW/2009-1/rainfall_IDW_2009-04-16T17-00-00.asc', archive=None, scheme=None), mode: w, driver: AAIGrid
2025-06-26 18:41:56,277 - rasterio._base - DEBUG - Nodata success: 1, Nodata value: -9999.000000
2025-06-26 18:41:56,277 - rasterio.env - DEBUG - Exiting env context: <rasterio.env.Env object at 0x0000018E20BCA7C8>
2025-06-26 18:41:56,277 - rasterio.env - DEBUG - Cleared existing <rasterio._env.GDALEnv object at 0x0000018E20BAA988> options
2025-06-26 18:41:56,278 - rasterio._env - DEBUG - Stopped GDALEnv <rasterio._env.GDALEnv object at 0x0000018E20BAA988>.
2025-06-26 18:41:56,278 - rasterio.env - DEBUG - Exiting outermost env
2025-06-26 18:41:56,279 - rasterio.env - DEBUG - Exited env context: <rasterio.env.Env object at 0x0000018E20BCA7C8>
2025-06-26 18:41:56,279 - rasterio.env - DEBUG - Entering env context: <rasterio.env.Env object at 0x0000018E20BCAA48>
2025-06-26 18:41:56,279 - rasterio.env - DEBUG - Starting outermost env
2025-06-26 18:41:56,280 - rasterio.env - DEBUG - No GDAL environment exists
2025-06-26 18:41:56,280 - rasterio.env - DEBUG - New GDAL environment <rasterio._env.GDALEnv object at 0x0000018E20BCA7C8> created
2025-06-26 18:41:56,282 - rasterio._env - DEBUG - GDAL_DATA found in environment.
2025-06-26 18:41:56,282 - rasterio._env - DEBUG - PROJ_LIB found in environment.
2025-06-26 18:41:56,283 - rasterio._env - DEBUG - Started GDALEnv: self=<rasterio._env.GDALEnv object at 0x0000018E20BCA7C8>.
2025-06-26 18:41:56,283 - rasterio.env - DEBUG - Entered env context: <rasterio.env.Env object at 0x0000018E20BCAA48>
2025-06-26 18:41:56,284 - rasterio._io - DEBUG - Skipped delete for overwrite. Dataset does not exist: 'D:/pythondata/jiangyuchazhi/output/IDW/2009-1/rainfall_IDW_2009-04-16T17-00-00.asc'
2025-06-26 18:41:56,285 - rasterio._io - DEBUG - Option: ('TILED', b'False')

2025-06-26 18:41:56,285 - rasterio._io - DEBUG - Option: ('COMPRESS', b'None')

2025-06-26 18:41:56,286 - rasterio._env - WARNING - CPLE_NotSupported in driver AAIGrid does not support creation option TILED
2025-06-26 18:41:56,286 - rasterio._env - WARNING - CPLE_NotSupported in driver AAIGrid does not support creation option COMPRESS
2025-06-26 18:41:56,296 - rasterio._io - DEBUG - Created copy from MEM file: D:/pythondata/jiangyuchazhi/output/IDW/2009-1/rainfall_IDW_2009-04-16T17-00-00.asc
2025-06-26 18:41:56,297 - rasterio.env - DEBUG - Exiting env context: <rasterio.env.Env object at 0x0000018E20BCAA48>
2025-06-26 18:41:56,297 - rasterio.env - DEBUG - Cleared existing <rasterio._env.GDALEnv object at 0x0000018E20BCA7C8> options
2025-06-26 18:41:56,298 - rasterio._env - DEBUG - Stopped GDALEnv <rasterio._env.GDALEnv object at 0x0000018E20BCA7C8>.
2025-06-26 18:41:56,298 - rasterio.env - DEBUG - Exiting outermost env
2025-06-26 18:41:56,298 - rasterio.env - DEBUG - Exited env context: <rasterio.env.Env object at 0x0000018E20BCAA48>
2025-06-26 18:41:56,299 - utils.raster_io - DEBUG - 成功写入栅格文件: D:\pythondata\jiangyuchazhi\output\IDW\2009-1\rainfall_IDW_2009-04-16T17-00-00.asc
2025-06-26 18:41:56,332 - core.idw - DEBUG - 所有站点值为零，返回零值栅格
2025-06-26 18:41:56,334 - rasterio.env - DEBUG - Entering env context: <rasterio.env.Env object at 0x0000018E20BCAEC8>
2025-06-26 18:41:56,341 - rasterio.env - DEBUG - Starting outermost env
2025-06-26 18:41:56,342 - rasterio.env - DEBUG - No GDAL environment exists
2025-06-26 18:41:56,342 - rasterio.env - DEBUG - New GDAL environment <rasterio._env.GDALEnv object at 0x0000018E20BCA7C8> created
2025-06-26 18:41:56,344 - rasterio._env - DEBUG - GDAL_DATA found in environment.
2025-06-26 18:41:56,344 - rasterio._env - DEBUG - PROJ_LIB found in environment.
2025-06-26 18:41:56,344 - rasterio._env - DEBUG - Started GDALEnv: self=<rasterio._env.GDALEnv object at 0x0000018E20BCA7C8>.
2025-06-26 18:41:56,345 - rasterio.env - DEBUG - Entered env context: <rasterio.env.Env object at 0x0000018E20BCAEC8>
2025-06-26 18:41:56,345 - rasterio._io - DEBUG - Path: ParsedPath(path='D:/pythondata/jiangyuchazhi/output/IDW/2009-1/rainfall_IDW_2009-04-16T18-00-00.asc', archive=None, scheme=None), mode: w, driver: AAIGrid
2025-06-26 18:41:56,346 - rasterio._base - DEBUG - Nodata success: 1, Nodata value: -9999.000000
2025-06-26 18:41:56,346 - rasterio.env - DEBUG - Exiting env context: <rasterio.env.Env object at 0x0000018E20BCAEC8>
2025-06-26 18:41:56,346 - rasterio.env - DEBUG - Cleared existing <rasterio._env.GDALEnv object at 0x0000018E20BCA7C8> options
2025-06-26 18:41:56,346 - rasterio._env - DEBUG - Stopped GDALEnv <rasterio._env.GDALEnv object at 0x0000018E20BCA7C8>.
2025-06-26 18:41:56,347 - rasterio.env - DEBUG - Exiting outermost env
2025-06-26 18:41:56,347 - rasterio.env - DEBUG - Exited env context: <rasterio.env.Env object at 0x0000018E20BCAEC8>
2025-06-26 18:41:56,347 - rasterio.env - DEBUG - Entering env context: <rasterio.env.Env object at 0x0000018E20BCB208>
2025-06-26 18:41:56,348 - rasterio.env - DEBUG - Starting outermost env
2025-06-26 18:41:56,348 - rasterio.env - DEBUG - No GDAL environment exists
2025-06-26 18:41:56,348 - rasterio.env - DEBUG - New GDAL environment <rasterio._env.GDALEnv object at 0x0000018E20BCAEC8> created
2025-06-26 18:41:56,350 - rasterio._env - DEBUG - GDAL_DATA found in environment.
2025-06-26 18:41:56,350 - rasterio._env - DEBUG - PROJ_LIB found in environment.
2025-06-26 18:41:56,351 - rasterio._env - DEBUG - Started GDALEnv: self=<rasterio._env.GDALEnv object at 0x0000018E20BCAEC8>.
2025-06-26 18:41:56,351 - rasterio.env - DEBUG - Entered env context: <rasterio.env.Env object at 0x0000018E20BCB208>
2025-06-26 18:41:56,352 - rasterio._io - DEBUG - Skipped delete for overwrite. Dataset does not exist: 'D:/pythondata/jiangyuchazhi/output/IDW/2009-1/rainfall_IDW_2009-04-16T18-00-00.asc'
2025-06-26 18:41:56,352 - rasterio._io - DEBUG - Option: ('TILED', b'False')

2025-06-26 18:41:56,352 - rasterio._io - DEBUG - Option: ('COMPRESS', b'None')

2025-06-26 18:41:56,353 - rasterio._env - WARNING - CPLE_NotSupported in driver AAIGrid does not support creation option TILED
2025-06-26 18:41:56,353 - rasterio._env - WARNING - CPLE_NotSupported in driver AAIGrid does not support creation option COMPRESS
2025-06-26 18:41:56,362 - rasterio._io - DEBUG - Created copy from MEM file: D:/pythondata/jiangyuchazhi/output/IDW/2009-1/rainfall_IDW_2009-04-16T18-00-00.asc
2025-06-26 18:41:56,363 - rasterio.env - DEBUG - Exiting env context: <rasterio.env.Env object at 0x0000018E20BCB208>
2025-06-26 18:41:56,364 - rasterio.env - DEBUG - Cleared existing <rasterio._env.GDALEnv object at 0x0000018E20BCAEC8> options
2025-06-26 18:41:56,364 - rasterio._env - DEBUG - Stopped GDALEnv <rasterio._env.GDALEnv object at 0x0000018E20BCAEC8>.
2025-06-26 18:41:56,365 - rasterio.env - DEBUG - Exiting outermost env
2025-06-26 18:41:56,365 - rasterio.env - DEBUG - Exited env context: <rasterio.env.Env object at 0x0000018E20BCB208>
2025-06-26 18:41:56,366 - utils.raster_io - DEBUG - 成功写入栅格文件: D:\pythondata\jiangyuchazhi\output\IDW\2009-1\rainfall_IDW_2009-04-16T18-00-00.asc
2025-06-26 18:41:56,366 - __main__ - INFO - 处理时间步 41/100: 2009-04-16 19:00:00
2025-06-26 18:41:56,395 - core.idw - DEBUG - 所有站点值为零，返回零值栅格
2025-06-26 18:41:56,396 - rasterio.env - DEBUG - Entering env context: <rasterio.env.Env object at 0x0000018E20BCB5C8>
2025-06-26 18:41:56,396 - rasterio.env - DEBUG - Starting outermost env
2025-06-26 18:41:56,396 - rasterio.env - DEBUG - No GDAL environment exists
2025-06-26 18:41:56,396 - rasterio.env - DEBUG - New GDAL environment <rasterio._env.GDALEnv object at 0x0000018E20BCBF88> created
2025-06-26 18:41:56,398 - rasterio._env - DEBUG - GDAL_DATA found in environment.
2025-06-26 18:41:56,398 - rasterio._env - DEBUG - PROJ_LIB found in environment.
2025-06-26 18:41:56,399 - rasterio._env - DEBUG - Started GDALEnv: self=<rasterio._env.GDALEnv object at 0x0000018E20BCBF88>.
2025-06-26 18:41:56,403 - rasterio.env - DEBUG - Entered env context: <rasterio.env.Env object at 0x0000018E20BCB5C8>
2025-06-26 18:41:56,404 - rasterio._io - DEBUG - Path: ParsedPath(path='D:/pythondata/jiangyuchazhi/output/IDW/2009-1/rainfall_IDW_2009-04-16T19-00-00.asc', archive=None, scheme=None), mode: w, driver: AAIGrid
2025-06-26 18:41:56,405 - rasterio._base - DEBUG - Nodata success: 1, Nodata value: -9999.000000
2025-06-26 18:41:56,406 - rasterio.env - DEBUG - Exiting env context: <rasterio.env.Env object at 0x0000018E20BCB5C8>
2025-06-26 18:41:56,406 - rasterio.env - DEBUG - Cleared existing <rasterio._env.GDALEnv object at 0x0000018E20BCBF88> options
2025-06-26 18:41:56,408 - rasterio._env - DEBUG - Stopped GDALEnv <rasterio._env.GDALEnv object at 0x0000018E20BCBF88>.
2025-06-26 18:41:56,408 - rasterio.env - DEBUG - Exiting outermost env
2025-06-26 18:41:56,410 - rasterio.env - DEBUG - Exited env context: <rasterio.env.Env object at 0x0000018E20BCB5C8>
2025-06-26 18:41:56,410 - rasterio.env - DEBUG - Entering env context: <rasterio.env.Env object at 0x0000018E20BAADC8>
2025-06-26 18:41:56,410 - rasterio.env - DEBUG - Starting outermost env
2025-06-26 18:41:56,410 - rasterio.env - DEBUG - No GDAL environment exists
2025-06-26 18:41:56,411 - rasterio.env - DEBUG - New GDAL environment <rasterio._env.GDALEnv object at 0x0000018E20BCB5C8> created
2025-06-26 18:41:56,412 - rasterio._env - DEBUG - GDAL_DATA found in environment.
2025-06-26 18:41:56,413 - rasterio._env - DEBUG - PROJ_LIB found in environment.
2025-06-26 18:41:56,413 - rasterio._env - DEBUG - Started GDALEnv: self=<rasterio._env.GDALEnv object at 0x0000018E20BCB5C8>.
2025-06-26 18:41:56,413 - rasterio.env - DEBUG - Entered env context: <rasterio.env.Env object at 0x0000018E20BAADC8>
2025-06-26 18:41:56,414 - rasterio._io - DEBUG - Skipped delete for overwrite. Dataset does not exist: 'D:/pythondata/jiangyuchazhi/output/IDW/2009-1/rainfall_IDW_2009-04-16T19-00-00.asc'
2025-06-26 18:41:56,414 - rasterio._io - DEBUG - Option: ('TILED', b'False')

2025-06-26 18:41:56,414 - rasterio._io - DEBUG - Option: ('COMPRESS', b'None')

2025-06-26 18:41:56,415 - rasterio._env - WARNING - CPLE_NotSupported in driver AAIGrid does not support creation option TILED
2025-06-26 18:41:56,416 - rasterio._env - WARNING - CPLE_NotSupported in driver AAIGrid does not support creation option COMPRESS
2025-06-26 18:41:56,427 - rasterio._io - DEBUG - Created copy from MEM file: D:/pythondata/jiangyuchazhi/output/IDW/2009-1/rainfall_IDW_2009-04-16T19-00-00.asc
2025-06-26 18:41:56,428 - rasterio.env - DEBUG - Exiting env context: <rasterio.env.Env object at 0x0000018E20BAADC8>
2025-06-26 18:41:56,428 - rasterio.env - DEBUG - Cleared existing <rasterio._env.GDALEnv object at 0x0000018E20BCB5C8> options
2025-06-26 18:41:56,428 - rasterio._env - DEBUG - Stopped GDALEnv <rasterio._env.GDALEnv object at 0x0000018E20BCB5C8>.
2025-06-26 18:41:56,429 - rasterio.env - DEBUG - Exiting outermost env
2025-06-26 18:41:56,429 - rasterio.env - DEBUG - Exited env context: <rasterio.env.Env object at 0x0000018E20BAADC8>
2025-06-26 18:41:56,429 - utils.raster_io - DEBUG - 成功写入栅格文件: D:\pythondata\jiangyuchazhi\output\IDW\2009-1\rainfall_IDW_2009-04-16T19-00-00.asc
2025-06-26 18:41:56,463 - core.idw - DEBUG - 所有站点值为零，返回零值栅格
2025-06-26 18:41:56,464 - rasterio.env - DEBUG - Entering env context: <rasterio.env.Env object at 0x0000018E20BCB208>
2025-06-26 18:41:56,464 - rasterio.env - DEBUG - Starting outermost env
2025-06-26 18:41:56,465 - rasterio.env - DEBUG - No GDAL environment exists
2025-06-26 18:41:56,465 - rasterio.env - DEBUG - New GDAL environment <rasterio._env.GDALEnv object at 0x0000018E20BCB548> created
2025-06-26 18:41:56,468 - rasterio._env - DEBUG - GDAL_DATA found in environment.
2025-06-26 18:41:56,469 - rasterio._env - DEBUG - PROJ_LIB found in environment.
2025-06-26 18:41:56,471 - rasterio._env - DEBUG - Started GDALEnv: self=<rasterio._env.GDALEnv object at 0x0000018E20BCB548>.
2025-06-26 18:41:56,471 - rasterio.env - DEBUG - Entered env context: <rasterio.env.Env object at 0x0000018E20BCB208>
2025-06-26 18:41:56,472 - rasterio._io - DEBUG - Path: ParsedPath(path='D:/pythondata/jiangyuchazhi/output/IDW/2009-1/rainfall_IDW_2009-04-16T20-00-00.asc', archive=None, scheme=None), mode: w, driver: AAIGrid
2025-06-26 18:41:56,475 - rasterio._base - DEBUG - Nodata success: 1, Nodata value: -9999.000000
2025-06-26 18:41:56,476 - rasterio.env - DEBUG - Exiting env context: <rasterio.env.Env object at 0x0000018E20BCB208>
2025-06-26 18:41:56,476 - rasterio.env - DEBUG - Cleared existing <rasterio._env.GDALEnv object at 0x0000018E20BCB548> options
2025-06-26 18:41:56,477 - rasterio._env - DEBUG - Stopped GDALEnv <rasterio._env.GDALEnv object at 0x0000018E20BCB548>.
2025-06-26 18:41:56,477 - rasterio.env - DEBUG - Exiting outermost env
2025-06-26 18:41:56,477 - rasterio.env - DEBUG - Exited env context: <rasterio.env.Env object at 0x0000018E20BCB208>
2025-06-26 18:41:56,477 - rasterio.env - DEBUG - Entering env context: <rasterio.env.Env object at 0x0000018E20B9D988>
2025-06-26 18:41:56,478 - rasterio.env - DEBUG - Starting outermost env
2025-06-26 18:41:56,478 - rasterio.env - DEBUG - No GDAL environment exists
2025-06-26 18:41:56,478 - rasterio.env - DEBUG - New GDAL environment <rasterio._env.GDALEnv object at 0x0000018E20BCB208> created
2025-06-26 18:41:56,479 - rasterio._env - DEBUG - GDAL_DATA found in environment.
2025-06-26 18:41:56,480 - rasterio._env - DEBUG - PROJ_LIB found in environment.
2025-06-26 18:41:56,480 - rasterio._env - DEBUG - Started GDALEnv: self=<rasterio._env.GDALEnv object at 0x0000018E20BCB208>.
2025-06-26 18:41:56,480 - rasterio.env - DEBUG - Entered env context: <rasterio.env.Env object at 0x0000018E20B9D988>
2025-06-26 18:41:56,482 - rasterio._io - DEBUG - Skipped delete for overwrite. Dataset does not exist: 'D:/pythondata/jiangyuchazhi/output/IDW/2009-1/rainfall_IDW_2009-04-16T20-00-00.asc'
2025-06-26 18:41:56,483 - rasterio._io - DEBUG - Option: ('TILED', b'False')

2025-06-26 18:41:56,483 - rasterio._io - DEBUG - Option: ('COMPRESS', b'None')

2025-06-26 18:41:56,494 - rasterio._env - WARNING - CPLE_NotSupported in driver AAIGrid does not support creation option TILED
2025-06-26 18:41:56,495 - rasterio._env - WARNING - CPLE_NotSupported in driver AAIGrid does not support creation option COMPRESS
2025-06-26 18:41:56,512 - rasterio._io - DEBUG - Created copy from MEM file: D:/pythondata/jiangyuchazhi/output/IDW/2009-1/rainfall_IDW_2009-04-16T20-00-00.asc
2025-06-26 18:41:56,512 - rasterio.env - DEBUG - Exiting env context: <rasterio.env.Env object at 0x0000018E20B9D988>
2025-06-26 18:41:56,513 - rasterio.env - DEBUG - Cleared existing <rasterio._env.GDALEnv object at 0x0000018E20BCB208> options
2025-06-26 18:41:56,513 - rasterio._env - DEBUG - Stopped GDALEnv <rasterio._env.GDALEnv object at 0x0000018E20BCB208>.
2025-06-26 18:41:56,513 - rasterio.env - DEBUG - Exiting outermost env
2025-06-26 18:41:56,514 - rasterio.env - DEBUG - Exited env context: <rasterio.env.Env object at 0x0000018E20B9D988>
2025-06-26 18:41:56,514 - utils.raster_io - DEBUG - 成功写入栅格文件: D:\pythondata\jiangyuchazhi\output\IDW\2009-1\rainfall_IDW_2009-04-16T20-00-00.asc
2025-06-26 18:41:56,560 - __main__ - WARNING - 生成时间步 2009-04-16 21:00:00 的栅格失败: boolean index did not match indexed array along dimension 0; dimension is 34 but corresponding boolean dimension is 46
2025-06-26 18:41:56,587 - core.idw - DEBUG - 所有站点值为零，返回零值栅格
2025-06-26 18:41:56,587 - rasterio.env - DEBUG - Entering env context: <rasterio.env.Env object at 0x0000018E20BCB548>
2025-06-26 18:41:56,588 - rasterio.env - DEBUG - Starting outermost env
2025-06-26 18:41:56,588 - rasterio.env - DEBUG - No GDAL environment exists
2025-06-26 18:41:56,588 - rasterio.env - DEBUG - New GDAL environment <rasterio._env.GDALEnv object at 0x0000018E20BAA988> created
2025-06-26 18:41:56,589 - rasterio._env - DEBUG - GDAL_DATA found in environment.
2025-06-26 18:41:56,590 - rasterio._env - DEBUG - PROJ_LIB found in environment.
2025-06-26 18:41:56,590 - rasterio._env - DEBUG - Started GDALEnv: self=<rasterio._env.GDALEnv object at 0x0000018E20BAA988>.
2025-06-26 18:41:56,590 - rasterio.env - DEBUG - Entered env context: <rasterio.env.Env object at 0x0000018E20BCB548>
2025-06-26 18:41:56,591 - rasterio._io - DEBUG - Path: ParsedPath(path='D:/pythondata/jiangyuchazhi/output/IDW/2009-1/rainfall_IDW_2009-04-16T22-00-00.asc', archive=None, scheme=None), mode: w, driver: AAIGrid
2025-06-26 18:41:56,591 - rasterio._base - DEBUG - Nodata success: 1, Nodata value: -9999.000000
2025-06-26 18:41:56,592 - rasterio.env - DEBUG - Exiting env context: <rasterio.env.Env object at 0x0000018E20BCB548>
2025-06-26 18:41:56,592 - rasterio.env - DEBUG - Cleared existing <rasterio._env.GDALEnv object at 0x0000018E20BAA988> options
2025-06-26 18:41:56,592 - rasterio._env - DEBUG - Stopped GDALEnv <rasterio._env.GDALEnv object at 0x0000018E20BAA988>.
2025-06-26 18:41:56,593 - rasterio.env - DEBUG - Exiting outermost env
2025-06-26 18:41:56,593 - rasterio.env - DEBUG - Exited env context: <rasterio.env.Env object at 0x0000018E20BCB548>
2025-06-26 18:41:56,593 - rasterio.env - DEBUG - Entering env context: <rasterio.env.Env object at 0x0000018E20BCB208>
2025-06-26 18:41:56,593 - rasterio.env - DEBUG - Starting outermost env
2025-06-26 18:41:56,593 - rasterio.env - DEBUG - No GDAL environment exists
2025-06-26 18:41:56,594 - rasterio.env - DEBUG - New GDAL environment <rasterio._env.GDALEnv object at 0x0000018E20BAA988> created
2025-06-26 18:41:56,595 - rasterio._env - DEBUG - GDAL_DATA found in environment.
2025-06-26 18:41:56,595 - rasterio._env - DEBUG - PROJ_LIB found in environment.
2025-06-26 18:41:56,595 - rasterio._env - DEBUG - Started GDALEnv: self=<rasterio._env.GDALEnv object at 0x0000018E20BAA988>.
2025-06-26 18:41:56,596 - rasterio.env - DEBUG - Entered env context: <rasterio.env.Env object at 0x0000018E20BCB208>
2025-06-26 18:41:56,596 - rasterio._io - DEBUG - Skipped delete for overwrite. Dataset does not exist: 'D:/pythondata/jiangyuchazhi/output/IDW/2009-1/rainfall_IDW_2009-04-16T22-00-00.asc'
2025-06-26 18:41:56,597 - rasterio._io - DEBUG - Option: ('TILED', b'False')

2025-06-26 18:41:56,597 - rasterio._io - DEBUG - Option: ('COMPRESS', b'None')

2025-06-26 18:41:56,598 - rasterio._env - WARNING - CPLE_NotSupported in driver AAIGrid does not support creation option TILED
2025-06-26 18:41:56,598 - rasterio._env - WARNING - CPLE_NotSupported in driver AAIGrid does not support creation option COMPRESS
2025-06-26 18:41:56,606 - rasterio._io - DEBUG - Created copy from MEM file: D:/pythondata/jiangyuchazhi/output/IDW/2009-1/rainfall_IDW_2009-04-16T22-00-00.asc
2025-06-26 18:41:56,608 - rasterio.env - DEBUG - Exiting env context: <rasterio.env.Env object at 0x0000018E20BCB208>
2025-06-26 18:41:56,609 - rasterio.env - DEBUG - Cleared existing <rasterio._env.GDALEnv object at 0x0000018E20BAA988> options
2025-06-26 18:41:56,609 - rasterio._env - DEBUG - Stopped GDALEnv <rasterio._env.GDALEnv object at 0x0000018E20BAA988>.
2025-06-26 18:41:56,609 - rasterio.env - DEBUG - Exiting outermost env
2025-06-26 18:41:56,610 - rasterio.env - DEBUG - Exited env context: <rasterio.env.Env object at 0x0000018E20BCB208>
2025-06-26 18:41:56,610 - utils.raster_io - DEBUG - 成功写入栅格文件: D:\pythondata\jiangyuchazhi\output\IDW\2009-1\rainfall_IDW_2009-04-16T22-00-00.asc
2025-06-26 18:41:56,640 - core.idw - DEBUG - 所有站点值为零，返回零值栅格
2025-06-26 18:41:56,644 - rasterio.env - DEBUG - Entering env context: <rasterio.env.Env object at 0x0000018E20BCB5C8>
2025-06-26 18:41:56,644 - rasterio.env - DEBUG - Starting outermost env
2025-06-26 18:41:56,644 - rasterio.env - DEBUG - No GDAL environment exists
2025-06-26 18:41:56,645 - rasterio.env - DEBUG - New GDAL environment <rasterio._env.GDALEnv object at 0x0000018E20BCB548> created
2025-06-26 18:41:56,646 - rasterio._env - DEBUG - GDAL_DATA found in environment.
2025-06-26 18:41:56,646 - rasterio._env - DEBUG - PROJ_LIB found in environment.
2025-06-26 18:41:56,646 - rasterio._env - DEBUG - Started GDALEnv: self=<rasterio._env.GDALEnv object at 0x0000018E20BCB548>.
2025-06-26 18:41:56,647 - rasterio.env - DEBUG - Entered env context: <rasterio.env.Env object at 0x0000018E20BCB5C8>
2025-06-26 18:41:56,647 - rasterio._io - DEBUG - Path: ParsedPath(path='D:/pythondata/jiangyuchazhi/output/IDW/2009-1/rainfall_IDW_2009-04-16T23-00-00.asc', archive=None, scheme=None), mode: w, driver: AAIGrid
2025-06-26 18:41:56,648 - rasterio._base - DEBUG - Nodata success: 1, Nodata value: -9999.000000
2025-06-26 18:41:56,648 - rasterio.env - DEBUG - Exiting env context: <rasterio.env.Env object at 0x0000018E20BCB5C8>
2025-06-26 18:41:56,648 - rasterio.env - DEBUG - Cleared existing <rasterio._env.GDALEnv object at 0x0000018E20BCB548> options
2025-06-26 18:41:56,648 - rasterio._env - DEBUG - Stopped GDALEnv <rasterio._env.GDALEnv object at 0x0000018E20BCB548>.
2025-06-26 18:41:56,649 - rasterio.env - DEBUG - Exiting outermost env
2025-06-26 18:41:56,649 - rasterio.env - DEBUG - Exited env context: <rasterio.env.Env object at 0x0000018E20BCB5C8>
2025-06-26 18:41:56,649 - rasterio.env - DEBUG - Entering env context: <rasterio.env.Env object at 0x0000018E20B9D988>
2025-06-26 18:41:56,656 - rasterio.env - DEBUG - Starting outermost env
2025-06-26 18:41:56,660 - rasterio.env - DEBUG - No GDAL environment exists
2025-06-26 18:41:56,660 - rasterio.env - DEBUG - New GDAL environment <rasterio._env.GDALEnv object at 0x0000018E20BCB548> created
2025-06-26 18:41:56,662 - rasterio._env - DEBUG - GDAL_DATA found in environment.
2025-06-26 18:41:56,662 - rasterio._env - DEBUG - PROJ_LIB found in environment.
2025-06-26 18:41:56,663 - rasterio._env - DEBUG - Started GDALEnv: self=<rasterio._env.GDALEnv object at 0x0000018E20BCB548>.
2025-06-26 18:41:56,663 - rasterio.env - DEBUG - Entered env context: <rasterio.env.Env object at 0x0000018E20B9D988>
2025-06-26 18:41:56,664 - rasterio._io - DEBUG - Skipped delete for overwrite. Dataset does not exist: 'D:/pythondata/jiangyuchazhi/output/IDW/2009-1/rainfall_IDW_2009-04-16T23-00-00.asc'
2025-06-26 18:41:56,664 - rasterio._io - DEBUG - Option: ('TILED', b'False')

2025-06-26 18:41:56,664 - rasterio._io - DEBUG - Option: ('COMPRESS', b'None')

2025-06-26 18:41:56,665 - rasterio._env - WARNING - CPLE_NotSupported in driver AAIGrid does not support creation option TILED
2025-06-26 18:41:56,665 - rasterio._env - WARNING - CPLE_NotSupported in driver AAIGrid does not support creation option COMPRESS
2025-06-26 18:41:56,682 - rasterio._io - DEBUG - Created copy from MEM file: D:/pythondata/jiangyuchazhi/output/IDW/2009-1/rainfall_IDW_2009-04-16T23-00-00.asc
2025-06-26 18:41:56,683 - rasterio.env - DEBUG - Exiting env context: <rasterio.env.Env object at 0x0000018E20B9D988>
2025-06-26 18:41:56,683 - rasterio.env - DEBUG - Cleared existing <rasterio._env.GDALEnv object at 0x0000018E20BCB548> options
2025-06-26 18:41:56,684 - rasterio._env - DEBUG - Stopped GDALEnv <rasterio._env.GDALEnv object at 0x0000018E20BCB548>.
2025-06-26 18:41:56,685 - rasterio.env - DEBUG - Exiting outermost env
2025-06-26 18:41:56,694 - rasterio.env - DEBUG - Exited env context: <rasterio.env.Env object at 0x0000018E20B9D988>
2025-06-26 18:41:56,695 - utils.raster_io - DEBUG - 成功写入栅格文件: D:\pythondata\jiangyuchazhi\output\IDW\2009-1\rainfall_IDW_2009-04-16T23-00-00.asc
2025-06-26 18:41:56,729 - core.idw - DEBUG - 所有站点值为零，返回零值栅格
2025-06-26 18:41:56,729 - rasterio.env - DEBUG - Entering env context: <rasterio.env.Env object at 0x0000018E20BCB208>
2025-06-26 18:41:56,730 - rasterio.env - DEBUG - Starting outermost env
2025-06-26 18:41:56,730 - rasterio.env - DEBUG - No GDAL environment exists
2025-06-26 18:41:56,730 - rasterio.env - DEBUG - New GDAL environment <rasterio._env.GDALEnv object at 0x0000018E20BCAEC8> created
2025-06-26 18:41:56,733 - rasterio._env - DEBUG - GDAL_DATA found in environment.
2025-06-26 18:41:56,736 - rasterio._env - DEBUG - PROJ_LIB found in environment.
2025-06-26 18:41:56,738 - rasterio._env - DEBUG - Started GDALEnv: self=<rasterio._env.GDALEnv object at 0x0000018E20BCAEC8>.
2025-06-26 18:41:56,739 - rasterio.env - DEBUG - Entered env context: <rasterio.env.Env object at 0x0000018E20BCB208>
2025-06-26 18:41:56,740 - rasterio._io - DEBUG - Path: ParsedPath(path='D:/pythondata/jiangyuchazhi/output/IDW/2009-1/rainfall_IDW_2009-04-17T00-00-00.asc', archive=None, scheme=None), mode: w, driver: AAIGrid
2025-06-26 18:41:56,741 - rasterio._base - DEBUG - Nodata success: 1, Nodata value: -9999.000000
2025-06-26 18:41:56,742 - rasterio.env - DEBUG - Exiting env context: <rasterio.env.Env object at 0x0000018E20BCB208>
2025-06-26 18:41:56,743 - rasterio.env - DEBUG - Cleared existing <rasterio._env.GDALEnv object at 0x0000018E20BCAEC8> options
2025-06-26 18:41:56,743 - rasterio._env - DEBUG - Stopped GDALEnv <rasterio._env.GDALEnv object at 0x0000018E20BCAEC8>.
2025-06-26 18:41:56,743 - rasterio.env - DEBUG - Exiting outermost env
2025-06-26 18:41:56,743 - rasterio.env - DEBUG - Exited env context: <rasterio.env.Env object at 0x0000018E20BCB208>
2025-06-26 18:41:56,744 - rasterio.env - DEBUG - Entering env context: <rasterio.env.Env object at 0x0000018E20BAA988>
2025-06-26 18:41:56,744 - rasterio.env - DEBUG - Starting outermost env
2025-06-26 18:41:56,744 - rasterio.env - DEBUG - No GDAL environment exists
2025-06-26 18:41:56,744 - rasterio.env - DEBUG - New GDAL environment <rasterio._env.GDALEnv object at 0x0000018E20BCB208> created
2025-06-26 18:41:56,746 - rasterio._env - DEBUG - GDAL_DATA found in environment.
2025-06-26 18:41:56,746 - rasterio._env - DEBUG - PROJ_LIB found in environment.
2025-06-26 18:41:56,746 - rasterio._env - DEBUG - Started GDALEnv: self=<rasterio._env.GDALEnv object at 0x0000018E20BCB208>.
2025-06-26 18:41:56,747 - rasterio.env - DEBUG - Entered env context: <rasterio.env.Env object at 0x0000018E20BAA988>
2025-06-26 18:41:56,748 - rasterio._io - DEBUG - Skipped delete for overwrite. Dataset does not exist: 'D:/pythondata/jiangyuchazhi/output/IDW/2009-1/rainfall_IDW_2009-04-17T00-00-00.asc'
2025-06-26 18:41:56,748 - rasterio._io - DEBUG - Option: ('TILED', b'False')

2025-06-26 18:41:56,749 - rasterio._io - DEBUG - Option: ('COMPRESS', b'None')

2025-06-26 18:41:56,751 - rasterio._env - WARNING - CPLE_NotSupported in driver AAIGrid does not support creation option TILED
2025-06-26 18:41:56,752 - rasterio._env - WARNING - CPLE_NotSupported in driver AAIGrid does not support creation option COMPRESS
2025-06-26 18:41:56,764 - rasterio._io - DEBUG - Created copy from MEM file: D:/pythondata/jiangyuchazhi/output/IDW/2009-1/rainfall_IDW_2009-04-17T00-00-00.asc
2025-06-26 18:41:56,765 - rasterio.env - DEBUG - Exiting env context: <rasterio.env.Env object at 0x0000018E20BAA988>
2025-06-26 18:41:56,765 - rasterio.env - DEBUG - Cleared existing <rasterio._env.GDALEnv object at 0x0000018E20BCB208> options
2025-06-26 18:41:56,766 - rasterio._env - DEBUG - Stopped GDALEnv <rasterio._env.GDALEnv object at 0x0000018E20BCB208>.
2025-06-26 18:41:56,768 - rasterio.env - DEBUG - Exiting outermost env
2025-06-26 18:41:56,769 - rasterio.env - DEBUG - Exited env context: <rasterio.env.Env object at 0x0000018E20BAA988>
2025-06-26 18:41:56,769 - utils.raster_io - DEBUG - 成功写入栅格文件: D:\pythondata\jiangyuchazhi\output\IDW\2009-1\rainfall_IDW_2009-04-17T00-00-00.asc
2025-06-26 18:41:56,801 - core.idw - DEBUG - 所有站点值为零，返回零值栅格
2025-06-26 18:41:56,802 - rasterio.env - DEBUG - Entering env context: <rasterio.env.Env object at 0x0000018E20BCB208>
2025-06-26 18:41:56,803 - rasterio.env - DEBUG - Starting outermost env
2025-06-26 18:41:56,803 - rasterio.env - DEBUG - No GDAL environment exists
2025-06-26 18:41:56,803 - rasterio.env - DEBUG - New GDAL environment <rasterio._env.GDALEnv object at 0x0000018E20BCC1C8> created
2025-06-26 18:41:56,806 - rasterio._env - DEBUG - GDAL_DATA found in environment.
2025-06-26 18:41:56,806 - rasterio._env - DEBUG - PROJ_LIB found in environment.
2025-06-26 18:41:56,807 - rasterio._env - DEBUG - Started GDALEnv: self=<rasterio._env.GDALEnv object at 0x0000018E20BCC1C8>.
2025-06-26 18:41:56,807 - rasterio.env - DEBUG - Entered env context: <rasterio.env.Env object at 0x0000018E20BCB208>
2025-06-26 18:41:56,808 - rasterio._io - DEBUG - Path: ParsedPath(path='D:/pythondata/jiangyuchazhi/output/IDW/2009-1/rainfall_IDW_2009-04-17T01-00-00.asc', archive=None, scheme=None), mode: w, driver: AAIGrid
2025-06-26 18:41:56,809 - rasterio._base - DEBUG - Nodata success: 1, Nodata value: -9999.000000
2025-06-26 18:41:56,809 - rasterio.env - DEBUG - Exiting env context: <rasterio.env.Env object at 0x0000018E20BCB208>
2025-06-26 18:41:56,810 - rasterio.env - DEBUG - Cleared existing <rasterio._env.GDALEnv object at 0x0000018E20BCC1C8> options
2025-06-26 18:41:56,810 - rasterio._env - DEBUG - Stopped GDALEnv <rasterio._env.GDALEnv object at 0x0000018E20BCC1C8>.
2025-06-26 18:41:56,810 - rasterio.env - DEBUG - Exiting outermost env
2025-06-26 18:41:56,811 - rasterio.env - DEBUG - Exited env context: <rasterio.env.Env object at 0x0000018E20BCB208>
2025-06-26 18:41:56,811 - rasterio.env - DEBUG - Entering env context: <rasterio.env.Env object at 0x0000018E20BCB5C8>
2025-06-26 18:41:56,811 - rasterio.env - DEBUG - Starting outermost env
2025-06-26 18:41:56,811 - rasterio.env - DEBUG - No GDAL environment exists
2025-06-26 18:41:56,811 - rasterio.env - DEBUG - New GDAL environment <rasterio._env.GDALEnv object at 0x0000018E20BCB208> created
2025-06-26 18:41:56,813 - rasterio._env - DEBUG - GDAL_DATA found in environment.
2025-06-26 18:41:56,813 - rasterio._env - DEBUG - PROJ_LIB found in environment.
2025-06-26 18:41:56,813 - rasterio._env - DEBUG - Started GDALEnv: self=<rasterio._env.GDALEnv object at 0x0000018E20BCB208>.
2025-06-26 18:41:56,814 - rasterio.env - DEBUG - Entered env context: <rasterio.env.Env object at 0x0000018E20BCB5C8>
2025-06-26 18:41:56,814 - rasterio._io - DEBUG - Skipped delete for overwrite. Dataset does not exist: 'D:/pythondata/jiangyuchazhi/output/IDW/2009-1/rainfall_IDW_2009-04-17T01-00-00.asc'
2025-06-26 18:41:56,815 - rasterio._io - DEBUG - Option: ('TILED', b'False')

2025-06-26 18:41:56,815 - rasterio._io - DEBUG - Option: ('COMPRESS', b'None')

2025-06-26 18:41:56,816 - rasterio._env - WARNING - CPLE_NotSupported in driver AAIGrid does not support creation option TILED
2025-06-26 18:41:56,827 - rasterio._env - WARNING - CPLE_NotSupported in driver AAIGrid does not support creation option COMPRESS
2025-06-26 18:41:56,833 - rasterio._io - DEBUG - Created copy from MEM file: D:/pythondata/jiangyuchazhi/output/IDW/2009-1/rainfall_IDW_2009-04-17T01-00-00.asc
2025-06-26 18:41:56,834 - rasterio.env - DEBUG - Exiting env context: <rasterio.env.Env object at 0x0000018E20BCB5C8>
2025-06-26 18:41:56,836 - rasterio.env - DEBUG - Cleared existing <rasterio._env.GDALEnv object at 0x0000018E20BCB208> options
2025-06-26 18:41:56,837 - rasterio._env - DEBUG - Stopped GDALEnv <rasterio._env.GDALEnv object at 0x0000018E20BCB208>.
2025-06-26 18:41:56,838 - rasterio.env - DEBUG - Exiting outermost env
2025-06-26 18:41:56,838 - rasterio.env - DEBUG - Exited env context: <rasterio.env.Env object at 0x0000018E20BCB5C8>
2025-06-26 18:41:56,839 - utils.raster_io - DEBUG - 成功写入栅格文件: D:\pythondata\jiangyuchazhi\output\IDW\2009-1\rainfall_IDW_2009-04-17T01-00-00.asc
2025-06-26 18:41:56,869 - core.idw - DEBUG - 所有站点值为零，返回零值栅格
2025-06-26 18:41:56,877 - rasterio.env - DEBUG - Entering env context: <rasterio.env.Env object at 0x0000018E20BAADC8>
2025-06-26 18:41:56,877 - rasterio.env - DEBUG - Starting outermost env
2025-06-26 18:41:56,877 - rasterio.env - DEBUG - No GDAL environment exists
2025-06-26 18:41:56,878 - rasterio.env - DEBUG - New GDAL environment <rasterio._env.GDALEnv object at 0x0000018E20BAA988> created
2025-06-26 18:41:56,879 - rasterio._env - DEBUG - GDAL_DATA found in environment.
2025-06-26 18:41:56,880 - rasterio._env - DEBUG - PROJ_LIB found in environment.
2025-06-26 18:41:56,880 - rasterio._env - DEBUG - Started GDALEnv: self=<rasterio._env.GDALEnv object at 0x0000018E20BAA988>.
2025-06-26 18:41:56,880 - rasterio.env - DEBUG - Entered env context: <rasterio.env.Env object at 0x0000018E20BAADC8>
2025-06-26 18:41:56,880 - rasterio._io - DEBUG - Path: ParsedPath(path='D:/pythondata/jiangyuchazhi/output/IDW/2009-1/rainfall_IDW_2009-04-17T02-00-00.asc', archive=None, scheme=None), mode: w, driver: AAIGrid
2025-06-26 18:41:56,881 - rasterio._base - DEBUG - Nodata success: 1, Nodata value: -9999.000000
2025-06-26 18:41:56,881 - rasterio.env - DEBUG - Exiting env context: <rasterio.env.Env object at 0x0000018E20BAADC8>
2025-06-26 18:41:56,881 - rasterio.env - DEBUG - Cleared existing <rasterio._env.GDALEnv object at 0x0000018E20BAA988> options
2025-06-26 18:41:56,882 - rasterio._env - DEBUG - Stopped GDALEnv <rasterio._env.GDALEnv object at 0x0000018E20BAA988>.
2025-06-26 18:41:56,883 - rasterio.env - DEBUG - Exiting outermost env
2025-06-26 18:41:56,883 - rasterio.env - DEBUG - Exited env context: <rasterio.env.Env object at 0x0000018E20BAADC8>
2025-06-26 18:41:56,883 - rasterio.env - DEBUG - Entering env context: <rasterio.env.Env object at 0x0000018E20BC7208>
2025-06-26 18:41:56,891 - rasterio.env - DEBUG - Starting outermost env
2025-06-26 18:41:56,893 - rasterio.env - DEBUG - No GDAL environment exists
2025-06-26 18:41:56,893 - rasterio.env - DEBUG - New GDAL environment <rasterio._env.GDALEnv object at 0x0000018E20BAADC8> created
2025-06-26 18:41:56,895 - rasterio._env - DEBUG - GDAL_DATA found in environment.
2025-06-26 18:41:56,895 - rasterio._env - DEBUG - PROJ_LIB found in environment.
2025-06-26 18:41:56,896 - rasterio._env - DEBUG - Started GDALEnv: self=<rasterio._env.GDALEnv object at 0x0000018E20BAADC8>.
2025-06-26 18:41:56,896 - rasterio.env - DEBUG - Entered env context: <rasterio.env.Env object at 0x0000018E20BC7208>
2025-06-26 18:41:56,897 - rasterio._io - DEBUG - Skipped delete for overwrite. Dataset does not exist: 'D:/pythondata/jiangyuchazhi/output/IDW/2009-1/rainfall_IDW_2009-04-17T02-00-00.asc'
2025-06-26 18:41:56,897 - rasterio._io - DEBUG - Option: ('TILED', b'False')

2025-06-26 18:41:56,897 - rasterio._io - DEBUG - Option: ('COMPRESS', b'None')

2025-06-26 18:41:56,898 - rasterio._env - WARNING - CPLE_NotSupported in driver AAIGrid does not support creation option TILED
2025-06-26 18:41:56,898 - rasterio._env - WARNING - CPLE_NotSupported in driver AAIGrid does not support creation option COMPRESS
2025-06-26 18:41:56,915 - rasterio._io - DEBUG - Created copy from MEM file: D:/pythondata/jiangyuchazhi/output/IDW/2009-1/rainfall_IDW_2009-04-17T02-00-00.asc
2025-06-26 18:41:56,916 - rasterio.env - DEBUG - Exiting env context: <rasterio.env.Env object at 0x0000018E20BC7208>
2025-06-26 18:41:56,916 - rasterio.env - DEBUG - Cleared existing <rasterio._env.GDALEnv object at 0x0000018E20BAADC8> options
2025-06-26 18:41:56,916 - rasterio._env - DEBUG - Stopped GDALEnv <rasterio._env.GDALEnv object at 0x0000018E20BAADC8>.
2025-06-26 18:41:56,917 - rasterio.env - DEBUG - Exiting outermost env
2025-06-26 18:41:56,917 - rasterio.env - DEBUG - Exited env context: <rasterio.env.Env object at 0x0000018E20BC7208>
2025-06-26 18:41:56,917 - utils.raster_io - DEBUG - 成功写入栅格文件: D:\pythondata\jiangyuchazhi\output\IDW\2009-1\rainfall_IDW_2009-04-17T02-00-00.asc
2025-06-26 18:41:56,964 - core.idw - DEBUG - 所有站点值为零，返回零值栅格
2025-06-26 18:41:56,965 - rasterio.env - DEBUG - Entering env context: <rasterio.env.Env object at 0x0000018E20BCC6C8>
2025-06-26 18:41:56,965 - rasterio.env - DEBUG - Starting outermost env
2025-06-26 18:41:56,965 - rasterio.env - DEBUG - No GDAL environment exists
2025-06-26 18:41:56,966 - rasterio.env - DEBUG - New GDAL environment <rasterio._env.GDALEnv object at 0x0000018E20BCCD08> created
2025-06-26 18:41:56,968 - rasterio._env - DEBUG - GDAL_DATA found in environment.
2025-06-26 18:41:56,971 - rasterio._env - DEBUG - PROJ_LIB found in environment.
2025-06-26 18:41:56,972 - rasterio._env - DEBUG - Started GDALEnv: self=<rasterio._env.GDALEnv object at 0x0000018E20BCCD08>.
2025-06-26 18:41:56,973 - rasterio.env - DEBUG - Entered env context: <rasterio.env.Env object at 0x0000018E20BCC6C8>
2025-06-26 18:41:56,975 - rasterio._io - DEBUG - Path: ParsedPath(path='D:/pythondata/jiangyuchazhi/output/IDW/2009-1/rainfall_IDW_2009-04-17T03-00-00.asc', archive=None, scheme=None), mode: w, driver: AAIGrid
2025-06-26 18:41:56,976 - rasterio._base - DEBUG - Nodata success: 1, Nodata value: -9999.000000
2025-06-26 18:41:56,976 - rasterio.env - DEBUG - Exiting env context: <rasterio.env.Env object at 0x0000018E20BCC6C8>
2025-06-26 18:41:56,976 - rasterio.env - DEBUG - Cleared existing <rasterio._env.GDALEnv object at 0x0000018E20BCCD08> options
2025-06-26 18:41:56,977 - rasterio._env - DEBUG - Stopped GDALEnv <rasterio._env.GDALEnv object at 0x0000018E20BCCD08>.
2025-06-26 18:41:56,977 - rasterio.env - DEBUG - Exiting outermost env
2025-06-26 18:41:56,977 - rasterio.env - DEBUG - Exited env context: <rasterio.env.Env object at 0x0000018E20BCC6C8>
2025-06-26 18:41:56,977 - rasterio.env - DEBUG - Entering env context: <rasterio.env.Env object at 0x0000018E20BCCC48>
2025-06-26 18:41:56,978 - rasterio.env - DEBUG - Starting outermost env
2025-06-26 18:41:56,978 - rasterio.env - DEBUG - No GDAL environment exists
2025-06-26 18:41:56,978 - rasterio.env - DEBUG - New GDAL environment <rasterio._env.GDALEnv object at 0x0000018E20BCC6C8> created
2025-06-26 18:41:56,979 - rasterio._env - DEBUG - GDAL_DATA found in environment.
2025-06-26 18:41:56,980 - rasterio._env - DEBUG - PROJ_LIB found in environment.
2025-06-26 18:41:56,980 - rasterio._env - DEBUG - Started GDALEnv: self=<rasterio._env.GDALEnv object at 0x0000018E20BCC6C8>.
2025-06-26 18:41:56,980 - rasterio.env - DEBUG - Entered env context: <rasterio.env.Env object at 0x0000018E20BCCC48>
2025-06-26 18:41:56,981 - rasterio._io - DEBUG - Skipped delete for overwrite. Dataset does not exist: 'D:/pythondata/jiangyuchazhi/output/IDW/2009-1/rainfall_IDW_2009-04-17T03-00-00.asc'
2025-06-26 18:41:56,981 - rasterio._io - DEBUG - Option: ('TILED', b'False')

2025-06-26 18:41:56,982 - rasterio._io - DEBUG - Option: ('COMPRESS', b'None')

2025-06-26 18:41:56,983 - rasterio._env - WARNING - CPLE_NotSupported in driver AAIGrid does not support creation option TILED
2025-06-26 18:41:56,994 - rasterio._env - WARNING - CPLE_NotSupported in driver AAIGrid does not support creation option COMPRESS
2025-06-26 18:41:57,010 - rasterio._io - DEBUG - Created copy from MEM file: D:/pythondata/jiangyuchazhi/output/IDW/2009-1/rainfall_IDW_2009-04-17T03-00-00.asc
2025-06-26 18:41:57,010 - rasterio.env - DEBUG - Exiting env context: <rasterio.env.Env object at 0x0000018E20BCCC48>
2025-06-26 18:41:57,011 - rasterio.env - DEBUG - Cleared existing <rasterio._env.GDALEnv object at 0x0000018E20BCC6C8> options
2025-06-26 18:41:57,011 - rasterio._env - DEBUG - Stopped GDALEnv <rasterio._env.GDALEnv object at 0x0000018E20BCC6C8>.
2025-06-26 18:41:57,011 - rasterio.env - DEBUG - Exiting outermost env
2025-06-26 18:41:57,011 - rasterio.env - DEBUG - Exited env context: <rasterio.env.Env object at 0x0000018E20BCCC48>
2025-06-26 18:41:57,012 - utils.raster_io - DEBUG - 成功写入栅格文件: D:\pythondata\jiangyuchazhi\output\IDW\2009-1\rainfall_IDW_2009-04-17T03-00-00.asc
2025-06-26 18:41:57,042 - core.idw - DEBUG - 所有站点值为零，返回零值栅格
2025-06-26 18:41:57,043 - rasterio.env - DEBUG - Entering env context: <rasterio.env.Env object at 0x0000018E20BCC0C8>
2025-06-26 18:41:57,043 - rasterio.env - DEBUG - Starting outermost env
2025-06-26 18:41:57,043 - rasterio.env - DEBUG - No GDAL environment exists
2025-06-26 18:41:57,043 - rasterio.env - DEBUG - New GDAL environment <rasterio._env.GDALEnv object at 0x0000018E20BCC208> created
2025-06-26 18:41:57,045 - rasterio._env - DEBUG - GDAL_DATA found in environment.
2025-06-26 18:41:57,045 - rasterio._env - DEBUG - PROJ_LIB found in environment.
2025-06-26 18:41:57,045 - rasterio._env - DEBUG - Started GDALEnv: self=<rasterio._env.GDALEnv object at 0x0000018E20BCC208>.
2025-06-26 18:41:57,046 - rasterio.env - DEBUG - Entered env context: <rasterio.env.Env object at 0x0000018E20BCC0C8>
2025-06-26 18:41:57,046 - rasterio._io - DEBUG - Path: ParsedPath(path='D:/pythondata/jiangyuchazhi/output/IDW/2009-1/rainfall_IDW_2009-04-17T04-00-00.asc', archive=None, scheme=None), mode: w, driver: AAIGrid
2025-06-26 18:41:57,046 - rasterio._base - DEBUG - Nodata success: 1, Nodata value: -9999.000000
2025-06-26 18:41:57,047 - rasterio.env - DEBUG - Exiting env context: <rasterio.env.Env object at 0x0000018E20BCC0C8>
2025-06-26 18:41:57,047 - rasterio.env - DEBUG - Cleared existing <rasterio._env.GDALEnv object at 0x0000018E20BCC208> options
2025-06-26 18:41:57,047 - rasterio._env - DEBUG - Stopped GDALEnv <rasterio._env.GDALEnv object at 0x0000018E20BCC208>.
2025-06-26 18:41:57,047 - rasterio.env - DEBUG - Exiting outermost env
2025-06-26 18:41:57,048 - rasterio.env - DEBUG - Exited env context: <rasterio.env.Env object at 0x0000018E20BCC0C8>
2025-06-26 18:41:57,048 - rasterio.env - DEBUG - Entering env context: <rasterio.env.Env object at 0x0000018E20BCC388>
2025-06-26 18:41:57,048 - rasterio.env - DEBUG - Starting outermost env
2025-06-26 18:41:57,049 - rasterio.env - DEBUG - No GDAL environment exists
2025-06-26 18:41:57,049 - rasterio.env - DEBUG - New GDAL environment <rasterio._env.GDALEnv object at 0x0000018E20BCC0C8> created
2025-06-26 18:41:57,051 - rasterio._env - DEBUG - GDAL_DATA found in environment.
2025-06-26 18:41:57,061 - rasterio._env - DEBUG - PROJ_LIB found in environment.
2025-06-26 18:41:57,062 - rasterio._env - DEBUG - Started GDALEnv: self=<rasterio._env.GDALEnv object at 0x0000018E20BCC0C8>.
2025-06-26 18:41:57,062 - rasterio.env - DEBUG - Entered env context: <rasterio.env.Env object at 0x0000018E20BCC388>
2025-06-26 18:41:57,063 - rasterio._io - DEBUG - Skipped delete for overwrite. Dataset does not exist: 'D:/pythondata/jiangyuchazhi/output/IDW/2009-1/rainfall_IDW_2009-04-17T04-00-00.asc'
2025-06-26 18:41:57,063 - rasterio._io - DEBUG - Option: ('TILED', b'False')

2025-06-26 18:41:57,063 - rasterio._io - DEBUG - Option: ('COMPRESS', b'None')

2025-06-26 18:41:57,064 - rasterio._env - WARNING - CPLE_NotSupported in driver AAIGrid does not support creation option TILED
2025-06-26 18:41:57,064 - rasterio._env - WARNING - CPLE_NotSupported in driver AAIGrid does not support creation option COMPRESS
2025-06-26 18:41:57,080 - rasterio._io - DEBUG - Created copy from MEM file: D:/pythondata/jiangyuchazhi/output/IDW/2009-1/rainfall_IDW_2009-04-17T04-00-00.asc
2025-06-26 18:41:57,080 - rasterio.env - DEBUG - Exiting env context: <rasterio.env.Env object at 0x0000018E20BCC388>
2025-06-26 18:41:57,080 - rasterio.env - DEBUG - Cleared existing <rasterio._env.GDALEnv object at 0x0000018E20BCC0C8> options
2025-06-26 18:41:57,081 - rasterio._env - DEBUG - Stopped GDALEnv <rasterio._env.GDALEnv object at 0x0000018E20BCC0C8>.
2025-06-26 18:41:57,081 - rasterio.env - DEBUG - Exiting outermost env
2025-06-26 18:41:57,081 - rasterio.env - DEBUG - Exited env context: <rasterio.env.Env object at 0x0000018E20BCC388>
2025-06-26 18:41:57,082 - utils.raster_io - DEBUG - 成功写入栅格文件: D:\pythondata\jiangyuchazhi\output\IDW\2009-1\rainfall_IDW_2009-04-17T04-00-00.asc
2025-06-26 18:41:57,083 - __main__ - INFO - 处理时间步 51/100: 2009-04-17 05:00:00
2025-06-26 18:41:57,113 - core.idw - DEBUG - 所有站点值为零，返回零值栅格
2025-06-26 18:41:57,114 - rasterio.env - DEBUG - Entering env context: <rasterio.env.Env object at 0x0000018E20BCCC48>
2025-06-26 18:41:57,114 - rasterio.env - DEBUG - Starting outermost env
2025-06-26 18:41:57,115 - rasterio.env - DEBUG - No GDAL environment exists
2025-06-26 18:41:57,115 - rasterio.env - DEBUG - New GDAL environment <rasterio._env.GDALEnv object at 0x0000018E20BCCD08> created
2025-06-26 18:41:57,118 - rasterio._env - DEBUG - GDAL_DATA found in environment.
2025-06-26 18:41:57,120 - rasterio._env - DEBUG - PROJ_LIB found in environment.
2025-06-26 18:41:57,121 - rasterio._env - DEBUG - Started GDALEnv: self=<rasterio._env.GDALEnv object at 0x0000018E20BCCD08>.
2025-06-26 18:41:57,122 - rasterio.env - DEBUG - Entered env context: <rasterio.env.Env object at 0x0000018E20BCCC48>
2025-06-26 18:41:57,123 - rasterio._io - DEBUG - Path: ParsedPath(path='D:/pythondata/jiangyuchazhi/output/IDW/2009-1/rainfall_IDW_2009-04-17T05-00-00.asc', archive=None, scheme=None), mode: w, driver: AAIGrid
2025-06-26 18:41:57,126 - rasterio._base - DEBUG - Nodata success: 1, Nodata value: -9999.000000
2025-06-26 18:41:57,126 - rasterio.env - DEBUG - Exiting env context: <rasterio.env.Env object at 0x0000018E20BCCC48>
2025-06-26 18:41:57,127 - rasterio.env - DEBUG - Cleared existing <rasterio._env.GDALEnv object at 0x0000018E20BCCD08> options
2025-06-26 18:41:57,127 - rasterio._env - DEBUG - Stopped GDALEnv <rasterio._env.GDALEnv object at 0x0000018E20BCCD08>.
2025-06-26 18:41:57,127 - rasterio.env - DEBUG - Exiting outermost env
2025-06-26 18:41:57,127 - rasterio.env - DEBUG - Exited env context: <rasterio.env.Env object at 0x0000018E20BCCC48>
2025-06-26 18:41:57,128 - rasterio.env - DEBUG - Entering env context: <rasterio.env.Env object at 0x0000018E20BCC1C8>
2025-06-26 18:41:57,128 - rasterio.env - DEBUG - Starting outermost env
2025-06-26 18:41:57,128 - rasterio.env - DEBUG - No GDAL environment exists
2025-06-26 18:41:57,128 - rasterio.env - DEBUG - New GDAL environment <rasterio._env.GDALEnv object at 0x0000018E20BCCC48> created
2025-06-26 18:41:57,130 - rasterio._env - DEBUG - GDAL_DATA found in environment.
2025-06-26 18:41:57,130 - rasterio._env - DEBUG - PROJ_LIB found in environment.
2025-06-26 18:41:57,130 - rasterio._env - DEBUG - Started GDALEnv: self=<rasterio._env.GDALEnv object at 0x0000018E20BCCC48>.
2025-06-26 18:41:57,130 - rasterio.env - DEBUG - Entered env context: <rasterio.env.Env object at 0x0000018E20BCC1C8>
2025-06-26 18:41:57,132 - rasterio._io - DEBUG - Skipped delete for overwrite. Dataset does not exist: 'D:/pythondata/jiangyuchazhi/output/IDW/2009-1/rainfall_IDW_2009-04-17T05-00-00.asc'
2025-06-26 18:41:57,132 - rasterio._io - DEBUG - Option: ('TILED', b'False')

2025-06-26 18:41:57,134 - rasterio._io - DEBUG - Option: ('COMPRESS', b'None')

2025-06-26 18:41:57,136 - rasterio._env - WARNING - CPLE_NotSupported in driver AAIGrid does not support creation option TILED
2025-06-26 18:41:57,138 - rasterio._env - WARNING - CPLE_NotSupported in driver AAIGrid does not support creation option COMPRESS
2025-06-26 18:41:57,148 - rasterio._io - DEBUG - Created copy from MEM file: D:/pythondata/jiangyuchazhi/output/IDW/2009-1/rainfall_IDW_2009-04-17T05-00-00.asc
2025-06-26 18:41:57,148 - rasterio.env - DEBUG - Exiting env context: <rasterio.env.Env object at 0x0000018E20BCC1C8>
2025-06-26 18:41:57,149 - rasterio.env - DEBUG - Cleared existing <rasterio._env.GDALEnv object at 0x0000018E20BCCC48> options
2025-06-26 18:41:57,149 - rasterio._env - DEBUG - Stopped GDALEnv <rasterio._env.GDALEnv object at 0x0000018E20BCCC48>.
2025-06-26 18:41:57,150 - rasterio.env - DEBUG - Exiting outermost env
2025-06-26 18:41:57,150 - rasterio.env - DEBUG - Exited env context: <rasterio.env.Env object at 0x0000018E20BCC1C8>
2025-06-26 18:41:57,150 - utils.raster_io - DEBUG - 成功写入栅格文件: D:\pythondata\jiangyuchazhi\output\IDW\2009-1\rainfall_IDW_2009-04-17T05-00-00.asc
2025-06-26 18:41:57,181 - core.idw - DEBUG - 所有站点值为零，返回零值栅格
2025-06-26 18:41:57,182 - rasterio.env - DEBUG - Entering env context: <rasterio.env.Env object at 0x0000018E20BCC208>
2025-06-26 18:41:57,182 - rasterio.env - DEBUG - Starting outermost env
2025-06-26 18:41:57,182 - rasterio.env - DEBUG - No GDAL environment exists
2025-06-26 18:41:57,183 - rasterio.env - DEBUG - New GDAL environment <rasterio._env.GDALEnv object at 0x0000018E20BCCF88> created
2025-06-26 18:41:57,185 - rasterio._env - DEBUG - GDAL_DATA found in environment.
2025-06-26 18:41:57,185 - rasterio._env - DEBUG - PROJ_LIB found in environment.
2025-06-26 18:41:57,187 - rasterio._env - DEBUG - Started GDALEnv: self=<rasterio._env.GDALEnv object at 0x0000018E20BCCF88>.
2025-06-26 18:41:57,188 - rasterio.env - DEBUG - Entered env context: <rasterio.env.Env object at 0x0000018E20BCC208>
2025-06-26 18:41:57,188 - rasterio._io - DEBUG - Path: ParsedPath(path='D:/pythondata/jiangyuchazhi/output/IDW/2009-1/rainfall_IDW_2009-04-17T06-00-00.asc', archive=None, scheme=None), mode: w, driver: AAIGrid
2025-06-26 18:41:57,189 - rasterio._base - DEBUG - Nodata success: 1, Nodata value: -9999.000000
2025-06-26 18:41:57,189 - rasterio.env - DEBUG - Exiting env context: <rasterio.env.Env object at 0x0000018E20BCC208>
2025-06-26 18:41:57,190 - rasterio.env - DEBUG - Cleared existing <rasterio._env.GDALEnv object at 0x0000018E20BCCF88> options
2025-06-26 18:41:57,190 - rasterio._env - DEBUG - Stopped GDALEnv <rasterio._env.GDALEnv object at 0x0000018E20BCCF88>.
2025-06-26 18:41:57,191 - rasterio.env - DEBUG - Exiting outermost env
2025-06-26 18:41:57,191 - rasterio.env - DEBUG - Exited env context: <rasterio.env.Env object at 0x0000018E20BCC208>
2025-06-26 18:41:57,192 - rasterio.env - DEBUG - Entering env context: <rasterio.env.Env object at 0x0000018E20BCCC48>
2025-06-26 18:41:57,192 - rasterio.env - DEBUG - Starting outermost env
2025-06-26 18:41:57,193 - rasterio.env - DEBUG - No GDAL environment exists
2025-06-26 18:41:57,194 - rasterio.env - DEBUG - New GDAL environment <rasterio._env.GDALEnv object at 0x0000018E20BCC208> created
2025-06-26 18:41:57,196 - rasterio._env - DEBUG - GDAL_DATA found in environment.
2025-06-26 18:41:57,196 - rasterio._env - DEBUG - PROJ_LIB found in environment.
2025-06-26 18:41:57,196 - rasterio._env - DEBUG - Started GDALEnv: self=<rasterio._env.GDALEnv object at 0x0000018E20BCC208>.
2025-06-26 18:41:57,197 - rasterio.env - DEBUG - Entered env context: <rasterio.env.Env object at 0x0000018E20BCCC48>
2025-06-26 18:41:57,198 - rasterio._io - DEBUG - Skipped delete for overwrite. Dataset does not exist: 'D:/pythondata/jiangyuchazhi/output/IDW/2009-1/rainfall_IDW_2009-04-17T06-00-00.asc'
2025-06-26 18:41:57,198 - rasterio._io - DEBUG - Option: ('TILED', b'False')

2025-06-26 18:41:57,198 - rasterio._io - DEBUG - Option: ('COMPRESS', b'None')

2025-06-26 18:41:57,199 - rasterio._env - WARNING - CPLE_NotSupported in driver AAIGrid does not support creation option TILED
2025-06-26 18:41:57,199 - rasterio._env - WARNING - CPLE_NotSupported in driver AAIGrid does not support creation option COMPRESS
2025-06-26 18:41:57,219 - rasterio._io - DEBUG - Created copy from MEM file: D:/pythondata/jiangyuchazhi/output/IDW/2009-1/rainfall_IDW_2009-04-17T06-00-00.asc
2025-06-26 18:41:57,225 - rasterio.env - DEBUG - Exiting env context: <rasterio.env.Env object at 0x0000018E20BCCC48>
2025-06-26 18:41:57,225 - rasterio.env - DEBUG - Cleared existing <rasterio._env.GDALEnv object at 0x0000018E20BCC208> options
2025-06-26 18:41:57,225 - rasterio._env - DEBUG - Stopped GDALEnv <rasterio._env.GDALEnv object at 0x0000018E20BCC208>.
2025-06-26 18:41:57,226 - rasterio.env - DEBUG - Exiting outermost env
2025-06-26 18:41:57,226 - rasterio.env - DEBUG - Exited env context: <rasterio.env.Env object at 0x0000018E20BCCC48>
2025-06-26 18:41:57,226 - utils.raster_io - DEBUG - 成功写入栅格文件: D:\pythondata\jiangyuchazhi\output\IDW\2009-1\rainfall_IDW_2009-04-17T06-00-00.asc
2025-06-26 18:41:57,272 - core.idw - DEBUG - 所有站点值为零，返回零值栅格
2025-06-26 18:41:57,273 - rasterio.env - DEBUG - Entering env context: <rasterio.env.Env object at 0x0000018E20BCC1C8>
2025-06-26 18:41:57,273 - rasterio.env - DEBUG - Starting outermost env
2025-06-26 18:41:57,274 - rasterio.env - DEBUG - No GDAL environment exists
2025-06-26 18:41:57,274 - rasterio.env - DEBUG - New GDAL environment <rasterio._env.GDALEnv object at 0x0000018E20BCCD88> created
2025-06-26 18:41:57,275 - rasterio._env - DEBUG - GDAL_DATA found in environment.
2025-06-26 18:41:57,275 - rasterio._env - DEBUG - PROJ_LIB found in environment.
2025-06-26 18:41:57,276 - rasterio._env - DEBUG - Started GDALEnv: self=<rasterio._env.GDALEnv object at 0x0000018E20BCCD88>.
2025-06-26 18:41:57,276 - rasterio.env - DEBUG - Entered env context: <rasterio.env.Env object at 0x0000018E20BCC1C8>
2025-06-26 18:41:57,276 - rasterio._io - DEBUG - Path: ParsedPath(path='D:/pythondata/jiangyuchazhi/output/IDW/2009-1/rainfall_IDW_2009-04-17T07-00-00.asc', archive=None, scheme=None), mode: w, driver: AAIGrid
2025-06-26 18:41:57,277 - rasterio._base - DEBUG - Nodata success: 1, Nodata value: -9999.000000
2025-06-26 18:41:57,277 - rasterio.env - DEBUG - Exiting env context: <rasterio.env.Env object at 0x0000018E20BCC1C8>
2025-06-26 18:41:57,277 - rasterio.env - DEBUG - Cleared existing <rasterio._env.GDALEnv object at 0x0000018E20BCCD88> options
2025-06-26 18:41:57,277 - rasterio._env - DEBUG - Stopped GDALEnv <rasterio._env.GDALEnv object at 0x0000018E20BCCD88>.
2025-06-26 18:41:57,278 - rasterio.env - DEBUG - Exiting outermost env
2025-06-26 18:41:57,278 - rasterio.env - DEBUG - Exited env context: <rasterio.env.Env object at 0x0000018E20BCC1C8>
2025-06-26 18:41:57,278 - rasterio.env - DEBUG - Entering env context: <rasterio.env.Env object at 0x0000018E20BCC208>
2025-06-26 18:41:57,278 - rasterio.env - DEBUG - Starting outermost env
2025-06-26 18:41:57,278 - rasterio.env - DEBUG - No GDAL environment exists
2025-06-26 18:41:57,279 - rasterio.env - DEBUG - New GDAL environment <rasterio._env.GDALEnv object at 0x0000018E20BCC1C8> created
2025-06-26 18:41:57,280 - rasterio._env - DEBUG - GDAL_DATA found in environment.
2025-06-26 18:41:57,281 - rasterio._env - DEBUG - PROJ_LIB found in environment.
2025-06-26 18:41:57,281 - rasterio._env - DEBUG - Started GDALEnv: self=<rasterio._env.GDALEnv object at 0x0000018E20BCC1C8>.
2025-06-26 18:41:57,281 - rasterio.env - DEBUG - Entered env context: <rasterio.env.Env object at 0x0000018E20BCC208>
2025-06-26 18:41:57,283 - rasterio._io - DEBUG - Skipped delete for overwrite. Dataset does not exist: 'D:/pythondata/jiangyuchazhi/output/IDW/2009-1/rainfall_IDW_2009-04-17T07-00-00.asc'
2025-06-26 18:41:57,283 - rasterio._io - DEBUG - Option: ('TILED', b'False')

2025-06-26 18:41:57,284 - rasterio._io - DEBUG - Option: ('COMPRESS', b'None')

2025-06-26 18:41:57,284 - rasterio._env - WARNING - CPLE_NotSupported in driver AAIGrid does not support creation option TILED
2025-06-26 18:41:57,284 - rasterio._env - WARNING - CPLE_NotSupported in driver AAIGrid does not support creation option COMPRESS
2025-06-26 18:41:57,375 - rasterio._io - DEBUG - Created copy from MEM file: D:/pythondata/jiangyuchazhi/output/IDW/2009-1/rainfall_IDW_2009-04-17T07-00-00.asc
2025-06-26 18:41:57,400 - rasterio.env - DEBUG - Exiting env context: <rasterio.env.Env object at 0x0000018E20BCC208>
2025-06-26 18:41:57,400 - rasterio.env - DEBUG - Cleared existing <rasterio._env.GDALEnv object at 0x0000018E20BCC1C8> options
2025-06-26 18:41:57,400 - rasterio._env - DEBUG - Stopped GDALEnv <rasterio._env.GDALEnv object at 0x0000018E20BCC1C8>.
2025-06-26 18:41:57,401 - rasterio.env - DEBUG - Exiting outermost env
2025-06-26 18:41:57,401 - rasterio.env - DEBUG - Exited env context: <rasterio.env.Env object at 0x0000018E20BCC208>
2025-06-26 18:41:57,401 - utils.raster_io - DEBUG - 成功写入栅格文件: D:\pythondata\jiangyuchazhi\output\IDW\2009-1\rainfall_IDW_2009-04-17T07-00-00.asc
2025-06-26 18:41:57,458 - core.idw - DEBUG - 所有站点值为零，返回零值栅格
2025-06-26 18:41:57,464 - rasterio.env - DEBUG - Entering env context: <rasterio.env.Env object at 0x0000018E20BCCC48>
2025-06-26 18:41:57,464 - rasterio.env - DEBUG - Starting outermost env
2025-06-26 18:41:57,464 - rasterio.env - DEBUG - No GDAL environment exists
2025-06-26 18:41:57,465 - rasterio.env - DEBUG - New GDAL environment <rasterio._env.GDALEnv object at 0x0000018E20BCB5C8> created
2025-06-26 18:41:57,468 - rasterio._env - DEBUG - GDAL_DATA found in environment.
2025-06-26 18:41:57,476 - rasterio._env - DEBUG - PROJ_LIB found in environment.
2025-06-26 18:41:57,476 - rasterio._env - DEBUG - Started GDALEnv: self=<rasterio._env.GDALEnv object at 0x0000018E20BCB5C8>.
2025-06-26 18:41:57,476 - rasterio.env - DEBUG - Entered env context: <rasterio.env.Env object at 0x0000018E20BCCC48>
2025-06-26 18:41:57,477 - rasterio._io - DEBUG - Path: ParsedPath(path='D:/pythondata/jiangyuchazhi/output/IDW/2009-1/rainfall_IDW_2009-04-17T08-00-00.asc', archive=None, scheme=None), mode: w, driver: AAIGrid
2025-06-26 18:41:57,478 - rasterio._base - DEBUG - Nodata success: 1, Nodata value: -9999.000000
2025-06-26 18:41:57,478 - rasterio.env - DEBUG - Exiting env context: <rasterio.env.Env object at 0x0000018E20BCCC48>
2025-06-26 18:41:57,478 - rasterio.env - DEBUG - Cleared existing <rasterio._env.GDALEnv object at 0x0000018E20BCB5C8> options
2025-06-26 18:41:57,478 - rasterio._env - DEBUG - Stopped GDALEnv <rasterio._env.GDALEnv object at 0x0000018E20BCB5C8>.
2025-06-26 18:41:57,478 - rasterio.env - DEBUG - Exiting outermost env
2025-06-26 18:41:57,479 - rasterio.env - DEBUG - Exited env context: <rasterio.env.Env object at 0x0000018E20BCCC48>
2025-06-26 18:41:57,479 - rasterio.env - DEBUG - Entering env context: <rasterio.env.Env object at 0x0000018E20BCC1C8>
2025-06-26 18:41:57,479 - rasterio.env - DEBUG - Starting outermost env
2025-06-26 18:41:57,479 - rasterio.env - DEBUG - No GDAL environment exists
2025-06-26 18:41:57,479 - rasterio.env - DEBUG - New GDAL environment <rasterio._env.GDALEnv object at 0x0000018E20BCCC48> created
2025-06-26 18:41:57,481 - rasterio._env - DEBUG - GDAL_DATA found in environment.
2025-06-26 18:41:57,481 - rasterio._env - DEBUG - PROJ_LIB found in environment.
2025-06-26 18:41:57,481 - rasterio._env - DEBUG - Started GDALEnv: self=<rasterio._env.GDALEnv object at 0x0000018E20BCCC48>.
2025-06-26 18:41:57,482 - rasterio.env - DEBUG - Entered env context: <rasterio.env.Env object at 0x0000018E20BCC1C8>
2025-06-26 18:41:57,483 - rasterio._io - DEBUG - Skipped delete for overwrite. Dataset does not exist: 'D:/pythondata/jiangyuchazhi/output/IDW/2009-1/rainfall_IDW_2009-04-17T08-00-00.asc'
2025-06-26 18:41:57,486 - rasterio._io - DEBUG - Option: ('TILED', b'False')

2025-06-26 18:41:57,488 - rasterio._io - DEBUG - Option: ('COMPRESS', b'None')

2025-06-26 18:41:57,489 - rasterio._env - WARNING - CPLE_NotSupported in driver AAIGrid does not support creation option TILED
2025-06-26 18:41:57,491 - rasterio._env - WARNING - CPLE_NotSupported in driver AAIGrid does not support creation option COMPRESS
2025-06-26 18:41:57,500 - rasterio._io - DEBUG - Created copy from MEM file: D:/pythondata/jiangyuchazhi/output/IDW/2009-1/rainfall_IDW_2009-04-17T08-00-00.asc
2025-06-26 18:41:57,502 - rasterio.env - DEBUG - Exiting env context: <rasterio.env.Env object at 0x0000018E20BCC1C8>
2025-06-26 18:41:57,503 - rasterio.env - DEBUG - Cleared existing <rasterio._env.GDALEnv object at 0x0000018E20BCCC48> options
2025-06-26 18:41:57,503 - rasterio._env - DEBUG - Stopped GDALEnv <rasterio._env.GDALEnv object at 0x0000018E20BCCC48>.
2025-06-26 18:41:57,505 - rasterio.env - DEBUG - Exiting outermost env
2025-06-26 18:41:57,506 - rasterio.env - DEBUG - Exited env context: <rasterio.env.Env object at 0x0000018E20BCC1C8>
2025-06-26 18:41:57,506 - utils.raster_io - DEBUG - 成功写入栅格文件: D:\pythondata\jiangyuchazhi\output\IDW\2009-1\rainfall_IDW_2009-04-17T08-00-00.asc
2025-06-26 18:41:57,544 - core.idw - DEBUG - 所有站点值为零，返回零值栅格
2025-06-26 18:41:57,545 - rasterio.env - DEBUG - Entering env context: <rasterio.env.Env object at 0x0000018E20BCC208>
2025-06-26 18:41:57,545 - rasterio.env - DEBUG - Starting outermost env
2025-06-26 18:41:57,546 - rasterio.env - DEBUG - No GDAL environment exists
2025-06-26 18:41:57,546 - rasterio.env - DEBUG - New GDAL environment <rasterio._env.GDALEnv object at 0x0000018E20BCC7C8> created
2025-06-26 18:41:57,547 - rasterio._env - DEBUG - GDAL_DATA found in environment.
2025-06-26 18:41:57,547 - rasterio._env - DEBUG - PROJ_LIB found in environment.
2025-06-26 18:41:57,548 - rasterio._env - DEBUG - Started GDALEnv: self=<rasterio._env.GDALEnv object at 0x0000018E20BCC7C8>.
2025-06-26 18:41:57,548 - rasterio.env - DEBUG - Entered env context: <rasterio.env.Env object at 0x0000018E20BCC208>
2025-06-26 18:41:57,549 - rasterio._io - DEBUG - Path: ParsedPath(path='D:/pythondata/jiangyuchazhi/output/IDW/2009-1/rainfall_IDW_2009-04-17T09-00-00.asc', archive=None, scheme=None), mode: w, driver: AAIGrid
2025-06-26 18:41:57,550 - rasterio._base - DEBUG - Nodata success: 1, Nodata value: -9999.000000
2025-06-26 18:41:57,550 - rasterio.env - DEBUG - Exiting env context: <rasterio.env.Env object at 0x0000018E20BCC208>
2025-06-26 18:41:57,550 - rasterio.env - DEBUG - Cleared existing <rasterio._env.GDALEnv object at 0x0000018E20BCC7C8> options
2025-06-26 18:41:57,551 - rasterio._env - DEBUG - Stopped GDALEnv <rasterio._env.GDALEnv object at 0x0000018E20BCC7C8>.
2025-06-26 18:41:57,551 - rasterio.env - DEBUG - Exiting outermost env
2025-06-26 18:41:57,551 - rasterio.env - DEBUG - Exited env context: <rasterio.env.Env object at 0x0000018E20BCC208>
2025-06-26 18:41:57,552 - rasterio.env - DEBUG - Entering env context: <rasterio.env.Env object at 0x0000018E20BCCC48>
2025-06-26 18:41:57,552 - rasterio.env - DEBUG - Starting outermost env
2025-06-26 18:41:57,552 - rasterio.env - DEBUG - No GDAL environment exists
2025-06-26 18:41:57,552 - rasterio.env - DEBUG - New GDAL environment <rasterio._env.GDALEnv object at 0x0000018E20BCC208> created
2025-06-26 18:41:57,554 - rasterio._env - DEBUG - GDAL_DATA found in environment.
2025-06-26 18:41:57,554 - rasterio._env - DEBUG - PROJ_LIB found in environment.
2025-06-26 18:41:57,554 - rasterio._env - DEBUG - Started GDALEnv: self=<rasterio._env.GDALEnv object at 0x0000018E20BCC208>.
2025-06-26 18:41:57,555 - rasterio.env - DEBUG - Entered env context: <rasterio.env.Env object at 0x0000018E20BCCC48>
2025-06-26 18:41:57,556 - rasterio._io - DEBUG - Skipped delete for overwrite. Dataset does not exist: 'D:/pythondata/jiangyuchazhi/output/IDW/2009-1/rainfall_IDW_2009-04-17T09-00-00.asc'
2025-06-26 18:41:57,556 - rasterio._io - DEBUG - Option: ('TILED', b'False')

2025-06-26 18:41:57,556 - rasterio._io - DEBUG - Option: ('COMPRESS', b'None')

2025-06-26 18:41:57,557 - rasterio._env - WARNING - CPLE_NotSupported in driver AAIGrid does not support creation option TILED
2025-06-26 18:41:57,557 - rasterio._env - WARNING - CPLE_NotSupported in driver AAIGrid does not support creation option COMPRESS
2025-06-26 18:41:57,564 - rasterio._io - DEBUG - Created copy from MEM file: D:/pythondata/jiangyuchazhi/output/IDW/2009-1/rainfall_IDW_2009-04-17T09-00-00.asc
2025-06-26 18:41:57,565 - rasterio.env - DEBUG - Exiting env context: <rasterio.env.Env object at 0x0000018E20BCCC48>
2025-06-26 18:41:57,565 - rasterio.env - DEBUG - Cleared existing <rasterio._env.GDALEnv object at 0x0000018E20BCC208> options
2025-06-26 18:41:57,565 - rasterio._env - DEBUG - Stopped GDALEnv <rasterio._env.GDALEnv object at 0x0000018E20BCC208>.
2025-06-26 18:41:57,566 - rasterio.env - DEBUG - Exiting outermost env
2025-06-26 18:41:57,566 - rasterio.env - DEBUG - Exited env context: <rasterio.env.Env object at 0x0000018E20BCCC48>
2025-06-26 18:41:57,567 - utils.raster_io - DEBUG - 成功写入栅格文件: D:\pythondata\jiangyuchazhi\output\IDW\2009-1\rainfall_IDW_2009-04-17T09-00-00.asc
2025-06-26 18:41:57,608 - core.idw - DEBUG - 所有站点值为零，返回零值栅格
2025-06-26 18:41:57,609 - rasterio.env - DEBUG - Entering env context: <rasterio.env.Env object at 0x0000018E20BCC1C8>
2025-06-26 18:41:57,609 - rasterio.env - DEBUG - Starting outermost env
2025-06-26 18:41:57,610 - rasterio.env - DEBUG - No GDAL environment exists
2025-06-26 18:41:57,610 - rasterio.env - DEBUG - New GDAL environment <rasterio._env.GDALEnv object at 0x0000018E20BCC208> created
2025-06-26 18:41:57,611 - rasterio._env - DEBUG - GDAL_DATA found in environment.
2025-06-26 18:41:57,611 - rasterio._env - DEBUG - PROJ_LIB found in environment.
2025-06-26 18:41:57,612 - rasterio._env - DEBUG - Started GDALEnv: self=<rasterio._env.GDALEnv object at 0x0000018E20BCC208>.
2025-06-26 18:41:57,612 - rasterio.env - DEBUG - Entered env context: <rasterio.env.Env object at 0x0000018E20BCC1C8>
2025-06-26 18:41:57,612 - rasterio._io - DEBUG - Path: ParsedPath(path='D:/pythondata/jiangyuchazhi/output/IDW/2009-1/rainfall_IDW_2009-04-17T10-00-00.asc', archive=None, scheme=None), mode: w, driver: AAIGrid
2025-06-26 18:41:57,613 - rasterio._base - DEBUG - Nodata success: 1, Nodata value: -9999.000000
2025-06-26 18:41:57,613 - rasterio.env - DEBUG - Exiting env context: <rasterio.env.Env object at 0x0000018E20BCC1C8>
2025-06-26 18:41:57,613 - rasterio.env - DEBUG - Cleared existing <rasterio._env.GDALEnv object at 0x0000018E20BCC208> options
2025-06-26 18:41:57,613 - rasterio._env - DEBUG - Stopped GDALEnv <rasterio._env.GDALEnv object at 0x0000018E20BCC208>.
2025-06-26 18:41:57,614 - rasterio.env - DEBUG - Exiting outermost env
2025-06-26 18:41:57,614 - rasterio.env - DEBUG - Exited env context: <rasterio.env.Env object at 0x0000018E20BCC1C8>
2025-06-26 18:41:57,614 - rasterio.env - DEBUG - Entering env context: <rasterio.env.Env object at 0x0000018E20BAA988>
2025-06-26 18:41:57,614 - rasterio.env - DEBUG - Starting outermost env
2025-06-26 18:41:57,615 - rasterio.env - DEBUG - No GDAL environment exists
2025-06-26 18:41:57,615 - rasterio.env - DEBUG - New GDAL environment <rasterio._env.GDALEnv object at 0x0000018E20BCC1C8> created
2025-06-26 18:41:57,617 - rasterio._env - DEBUG - GDAL_DATA found in environment.
2025-06-26 18:41:57,618 - rasterio._env - DEBUG - PROJ_LIB found in environment.
2025-06-26 18:41:57,618 - rasterio._env - DEBUG - Started GDALEnv: self=<rasterio._env.GDALEnv object at 0x0000018E20BCC1C8>.
2025-06-26 18:41:57,618 - rasterio.env - DEBUG - Entered env context: <rasterio.env.Env object at 0x0000018E20BAA988>
2025-06-26 18:41:57,619 - rasterio._io - DEBUG - Skipped delete for overwrite. Dataset does not exist: 'D:/pythondata/jiangyuchazhi/output/IDW/2009-1/rainfall_IDW_2009-04-17T10-00-00.asc'
2025-06-26 18:41:57,620 - rasterio._io - DEBUG - Option: ('TILED', b'False')

2025-06-26 18:41:57,627 - rasterio._io - DEBUG - Option: ('COMPRESS', b'None')

2025-06-26 18:41:57,629 - rasterio._env - WARNING - CPLE_NotSupported in driver AAIGrid does not support creation option TILED
2025-06-26 18:41:57,629 - rasterio._env - WARNING - CPLE_NotSupported in driver AAIGrid does not support creation option COMPRESS
2025-06-26 18:41:57,647 - rasterio._io - DEBUG - Created copy from MEM file: D:/pythondata/jiangyuchazhi/output/IDW/2009-1/rainfall_IDW_2009-04-17T10-00-00.asc
2025-06-26 18:41:57,648 - rasterio.env - DEBUG - Exiting env context: <rasterio.env.Env object at 0x0000018E20BAA988>
2025-06-26 18:41:57,648 - rasterio.env - DEBUG - Cleared existing <rasterio._env.GDALEnv object at 0x0000018E20BCC1C8> options
2025-06-26 18:41:57,649 - rasterio._env - DEBUG - Stopped GDALEnv <rasterio._env.GDALEnv object at 0x0000018E20BCC1C8>.
2025-06-26 18:41:57,649 - rasterio.env - DEBUG - Exiting outermost env
2025-06-26 18:41:57,649 - rasterio.env - DEBUG - Exited env context: <rasterio.env.Env object at 0x0000018E20BAA988>
2025-06-26 18:41:57,650 - utils.raster_io - DEBUG - 成功写入栅格文件: D:\pythondata\jiangyuchazhi\output\IDW\2009-1\rainfall_IDW_2009-04-17T10-00-00.asc
2025-06-26 18:41:57,683 - core.idw - DEBUG - 所有站点值为零，返回零值栅格
2025-06-26 18:41:57,684 - rasterio.env - DEBUG - Entering env context: <rasterio.env.Env object at 0x0000018E20BCC7C8>
2025-06-26 18:41:57,684 - rasterio.env - DEBUG - Starting outermost env
2025-06-26 18:41:57,684 - rasterio.env - DEBUG - No GDAL environment exists
2025-06-26 18:41:57,685 - rasterio.env - DEBUG - New GDAL environment <rasterio._env.GDALEnv object at 0x0000018E20BCC1C8> created
2025-06-26 18:41:57,686 - rasterio._env - DEBUG - GDAL_DATA found in environment.
2025-06-26 18:41:57,686 - rasterio._env - DEBUG - PROJ_LIB found in environment.
2025-06-26 18:41:57,687 - rasterio._env - DEBUG - Started GDALEnv: self=<rasterio._env.GDALEnv object at 0x0000018E20BCC1C8>.
2025-06-26 18:41:57,687 - rasterio.env - DEBUG - Entered env context: <rasterio.env.Env object at 0x0000018E20BCC7C8>
2025-06-26 18:41:57,687 - rasterio._io - DEBUG - Path: ParsedPath(path='D:/pythondata/jiangyuchazhi/output/IDW/2009-1/rainfall_IDW_2009-04-17T11-00-00.asc', archive=None, scheme=None), mode: w, driver: AAIGrid
2025-06-26 18:41:57,688 - rasterio._base - DEBUG - Nodata success: 1, Nodata value: -9999.000000
2025-06-26 18:41:57,688 - rasterio.env - DEBUG - Exiting env context: <rasterio.env.Env object at 0x0000018E20BCC7C8>
2025-06-26 18:41:57,688 - rasterio.env - DEBUG - Cleared existing <rasterio._env.GDALEnv object at 0x0000018E20BCC1C8> options
2025-06-26 18:41:57,688 - rasterio._env - DEBUG - Stopped GDALEnv <rasterio._env.GDALEnv object at 0x0000018E20BCC1C8>.
2025-06-26 18:41:57,688 - rasterio.env - DEBUG - Exiting outermost env
2025-06-26 18:41:57,689 - rasterio.env - DEBUG - Exited env context: <rasterio.env.Env object at 0x0000018E20BCC7C8>
2025-06-26 18:41:57,689 - rasterio.env - DEBUG - Entering env context: <rasterio.env.Env object at 0x0000018E20BC7208>
2025-06-26 18:41:57,689 - rasterio.env - DEBUG - Starting outermost env
2025-06-26 18:41:57,690 - rasterio.env - DEBUG - No GDAL environment exists
2025-06-26 18:41:57,690 - rasterio.env - DEBUG - New GDAL environment <rasterio._env.GDALEnv object at 0x0000018E20BCC7C8> created
2025-06-26 18:41:57,693 - rasterio._env - DEBUG - GDAL_DATA found in environment.
2025-06-26 18:41:57,695 - rasterio._env - DEBUG - PROJ_LIB found in environment.
2025-06-26 18:41:57,695 - rasterio._env - DEBUG - Started GDALEnv: self=<rasterio._env.GDALEnv object at 0x0000018E20BCC7C8>.
2025-06-26 18:41:57,696 - rasterio.env - DEBUG - Entered env context: <rasterio.env.Env object at 0x0000018E20BC7208>
2025-06-26 18:41:57,697 - rasterio._io - DEBUG - Skipped delete for overwrite. Dataset does not exist: 'D:/pythondata/jiangyuchazhi/output/IDW/2009-1/rainfall_IDW_2009-04-17T11-00-00.asc'
2025-06-26 18:41:57,698 - rasterio._io - DEBUG - Option: ('TILED', b'False')

2025-06-26 18:41:57,698 - rasterio._io - DEBUG - Option: ('COMPRESS', b'None')

2025-06-26 18:41:57,700 - rasterio._env - WARNING - CPLE_NotSupported in driver AAIGrid does not support creation option TILED
2025-06-26 18:41:57,700 - rasterio._env - WARNING - CPLE_NotSupported in driver AAIGrid does not support creation option COMPRESS
2025-06-26 18:41:57,707 - rasterio._io - DEBUG - Created copy from MEM file: D:/pythondata/jiangyuchazhi/output/IDW/2009-1/rainfall_IDW_2009-04-17T11-00-00.asc
2025-06-26 18:41:57,707 - rasterio.env - DEBUG - Exiting env context: <rasterio.env.Env object at 0x0000018E20BC7208>
2025-06-26 18:41:57,708 - rasterio.env - DEBUG - Cleared existing <rasterio._env.GDALEnv object at 0x0000018E20BCC7C8> options
2025-06-26 18:41:57,708 - rasterio._env - DEBUG - Stopped GDALEnv <rasterio._env.GDALEnv object at 0x0000018E20BCC7C8>.
2025-06-26 18:41:57,708 - rasterio.env - DEBUG - Exiting outermost env
2025-06-26 18:41:57,708 - rasterio.env - DEBUG - Exited env context: <rasterio.env.Env object at 0x0000018E20BC7208>
2025-06-26 18:41:57,709 - utils.raster_io - DEBUG - 成功写入栅格文件: D:\pythondata\jiangyuchazhi\output\IDW\2009-1\rainfall_IDW_2009-04-17T11-00-00.asc
2025-06-26 18:41:57,744 - core.idw - DEBUG - 所有站点值为零，返回零值栅格
2025-06-26 18:41:57,746 - rasterio.env - DEBUG - Entering env context: <rasterio.env.Env object at 0x0000018E20BCC208>
2025-06-26 18:41:57,747 - rasterio.env - DEBUG - Starting outermost env
2025-06-26 18:41:57,747 - rasterio.env - DEBUG - No GDAL environment exists
2025-06-26 18:41:57,747 - rasterio.env - DEBUG - New GDAL environment <rasterio._env.GDALEnv object at 0x0000018E20B9D988> created
2025-06-26 18:41:57,748 - rasterio._env - DEBUG - GDAL_DATA found in environment.
2025-06-26 18:41:57,749 - rasterio._env - DEBUG - PROJ_LIB found in environment.
2025-06-26 18:41:57,749 - rasterio._env - DEBUG - Started GDALEnv: self=<rasterio._env.GDALEnv object at 0x0000018E20B9D988>.
2025-06-26 18:41:57,749 - rasterio.env - DEBUG - Entered env context: <rasterio.env.Env object at 0x0000018E20BCC208>
2025-06-26 18:41:57,750 - rasterio._io - DEBUG - Path: ParsedPath(path='D:/pythondata/jiangyuchazhi/output/IDW/2009-1/rainfall_IDW_2009-04-17T12-00-00.asc', archive=None, scheme=None), mode: w, driver: AAIGrid
2025-06-26 18:41:57,750 - rasterio._base - DEBUG - Nodata success: 1, Nodata value: -9999.000000
2025-06-26 18:41:57,750 - rasterio.env - DEBUG - Exiting env context: <rasterio.env.Env object at 0x0000018E20BCC208>
2025-06-26 18:41:57,751 - rasterio.env - DEBUG - Cleared existing <rasterio._env.GDALEnv object at 0x0000018E20B9D988> options
2025-06-26 18:41:57,751 - rasterio._env - DEBUG - Stopped GDALEnv <rasterio._env.GDALEnv object at 0x0000018E20B9D988>.
2025-06-26 18:41:57,751 - rasterio.env - DEBUG - Exiting outermost env
2025-06-26 18:41:57,751 - rasterio.env - DEBUG - Exited env context: <rasterio.env.Env object at 0x0000018E20BCC208>
2025-06-26 18:41:57,752 - rasterio.env - DEBUG - Entering env context: <rasterio.env.Env object at 0x0000018E20BCC7C8>
2025-06-26 18:41:57,754 - rasterio.env - DEBUG - Starting outermost env
2025-06-26 18:41:57,755 - rasterio.env - DEBUG - No GDAL environment exists
2025-06-26 18:41:57,755 - rasterio.env - DEBUG - New GDAL environment <rasterio._env.GDALEnv object at 0x0000018E20BCC208> created
2025-06-26 18:41:57,758 - rasterio._env - DEBUG - GDAL_DATA found in environment.
2025-06-26 18:41:57,758 - rasterio._env - DEBUG - PROJ_LIB found in environment.
2025-06-26 18:41:57,758 - rasterio._env - DEBUG - Started GDALEnv: self=<rasterio._env.GDALEnv object at 0x0000018E20BCC208>.
2025-06-26 18:41:57,759 - rasterio.env - DEBUG - Entered env context: <rasterio.env.Env object at 0x0000018E20BCC7C8>
2025-06-26 18:41:57,760 - rasterio._io - DEBUG - Skipped delete for overwrite. Dataset does not exist: 'D:/pythondata/jiangyuchazhi/output/IDW/2009-1/rainfall_IDW_2009-04-17T12-00-00.asc'
2025-06-26 18:41:57,762 - rasterio._io - DEBUG - Option: ('TILED', b'False')

2025-06-26 18:41:57,762 - rasterio._io - DEBUG - Option: ('COMPRESS', b'None')

2025-06-26 18:41:57,763 - rasterio._env - WARNING - CPLE_NotSupported in driver AAIGrid does not support creation option TILED
2025-06-26 18:41:57,763 - rasterio._env - WARNING - CPLE_NotSupported in driver AAIGrid does not support creation option COMPRESS
2025-06-26 18:41:57,781 - rasterio._io - DEBUG - Created copy from MEM file: D:/pythondata/jiangyuchazhi/output/IDW/2009-1/rainfall_IDW_2009-04-17T12-00-00.asc
2025-06-26 18:41:57,781 - rasterio.env - DEBUG - Exiting env context: <rasterio.env.Env object at 0x0000018E20BCC7C8>
2025-06-26 18:41:57,782 - rasterio.env - DEBUG - Cleared existing <rasterio._env.GDALEnv object at 0x0000018E20BCC208> options
2025-06-26 18:41:57,783 - rasterio._env - DEBUG - Stopped GDALEnv <rasterio._env.GDALEnv object at 0x0000018E20BCC208>.
2025-06-26 18:41:57,784 - rasterio.env - DEBUG - Exiting outermost env
2025-06-26 18:41:57,784 - rasterio.env - DEBUG - Exited env context: <rasterio.env.Env object at 0x0000018E20BCC7C8>
2025-06-26 18:41:57,785 - utils.raster_io - DEBUG - 成功写入栅格文件: D:\pythondata\jiangyuchazhi\output\IDW\2009-1\rainfall_IDW_2009-04-17T12-00-00.asc
2025-06-26 18:41:57,816 - core.idw - DEBUG - 所有站点值为零，返回零值栅格
2025-06-26 18:41:57,826 - rasterio.env - DEBUG - Entering env context: <rasterio.env.Env object at 0x0000018E20BCC1C8>
2025-06-26 18:41:57,827 - rasterio.env - DEBUG - Starting outermost env
2025-06-26 18:41:57,827 - rasterio.env - DEBUG - No GDAL environment exists
2025-06-26 18:41:57,827 - rasterio.env - DEBUG - New GDAL environment <rasterio._env.GDALEnv object at 0x0000018E20BC8408> created
2025-06-26 18:41:57,828 - rasterio._env - DEBUG - GDAL_DATA found in environment.
2025-06-26 18:41:57,829 - rasterio._env - DEBUG - PROJ_LIB found in environment.
2025-06-26 18:41:57,829 - rasterio._env - DEBUG - Started GDALEnv: self=<rasterio._env.GDALEnv object at 0x0000018E20BC8408>.
2025-06-26 18:41:57,829 - rasterio.env - DEBUG - Entered env context: <rasterio.env.Env object at 0x0000018E20BCC1C8>
2025-06-26 18:41:57,830 - rasterio._io - DEBUG - Path: ParsedPath(path='D:/pythondata/jiangyuchazhi/output/IDW/2009-1/rainfall_IDW_2009-04-17T13-00-00.asc', archive=None, scheme=None), mode: w, driver: AAIGrid
2025-06-26 18:41:57,830 - rasterio._base - DEBUG - Nodata success: 1, Nodata value: -9999.000000
2025-06-26 18:41:57,830 - rasterio.env - DEBUG - Exiting env context: <rasterio.env.Env object at 0x0000018E20BCC1C8>
2025-06-26 18:41:57,831 - rasterio.env - DEBUG - Cleared existing <rasterio._env.GDALEnv object at 0x0000018E20BC8408> options
2025-06-26 18:41:57,831 - rasterio._env - DEBUG - Stopped GDALEnv <rasterio._env.GDALEnv object at 0x0000018E20BC8408>.
2025-06-26 18:41:57,831 - rasterio.env - DEBUG - Exiting outermost env
2025-06-26 18:41:57,831 - rasterio.env - DEBUG - Exited env context: <rasterio.env.Env object at 0x0000018E20BCC1C8>
2025-06-26 18:41:57,832 - rasterio.env - DEBUG - Entering env context: <rasterio.env.Env object at 0x0000018E20BCC208>
2025-06-26 18:41:57,832 - rasterio.env - DEBUG - Starting outermost env
2025-06-26 18:41:57,832 - rasterio.env - DEBUG - No GDAL environment exists
2025-06-26 18:41:57,832 - rasterio.env - DEBUG - New GDAL environment <rasterio._env.GDALEnv object at 0x0000018E20BCC1C8> created
2025-06-26 18:41:57,835 - rasterio._env - DEBUG - GDAL_DATA found in environment.
2025-06-26 18:41:57,836 - rasterio._env - DEBUG - PROJ_LIB found in environment.
2025-06-26 18:41:57,837 - rasterio._env - DEBUG - Started GDALEnv: self=<rasterio._env.GDALEnv object at 0x0000018E20BCC1C8>.
2025-06-26 18:41:57,839 - rasterio.env - DEBUG - Entered env context: <rasterio.env.Env object at 0x0000018E20BCC208>
2025-06-26 18:41:57,842 - rasterio._io - DEBUG - Skipped delete for overwrite. Dataset does not exist: 'D:/pythondata/jiangyuchazhi/output/IDW/2009-1/rainfall_IDW_2009-04-17T13-00-00.asc'
2025-06-26 18:41:57,842 - rasterio._io - DEBUG - Option: ('TILED', b'False')

2025-06-26 18:41:57,843 - rasterio._io - DEBUG - Option: ('COMPRESS', b'None')

2025-06-26 18:41:57,844 - rasterio._env - WARNING - CPLE_NotSupported in driver AAIGrid does not support creation option TILED
2025-06-26 18:41:57,844 - rasterio._env - WARNING - CPLE_NotSupported in driver AAIGrid does not support creation option COMPRESS
2025-06-26 18:41:57,860 - rasterio._io - DEBUG - Created copy from MEM file: D:/pythondata/jiangyuchazhi/output/IDW/2009-1/rainfall_IDW_2009-04-17T13-00-00.asc
2025-06-26 18:41:57,860 - rasterio.env - DEBUG - Exiting env context: <rasterio.env.Env object at 0x0000018E20BCC208>
2025-06-26 18:41:57,860 - rasterio.env - DEBUG - Cleared existing <rasterio._env.GDALEnv object at 0x0000018E20BCC1C8> options
2025-06-26 18:41:57,861 - rasterio._env - DEBUG - Stopped GDALEnv <rasterio._env.GDALEnv object at 0x0000018E20BCC1C8>.
2025-06-26 18:41:57,861 - rasterio.env - DEBUG - Exiting outermost env
2025-06-26 18:41:57,861 - rasterio.env - DEBUG - Exited env context: <rasterio.env.Env object at 0x0000018E20BCC208>
2025-06-26 18:41:57,861 - utils.raster_io - DEBUG - 成功写入栅格文件: D:\pythondata\jiangyuchazhi\output\IDW\2009-1\rainfall_IDW_2009-04-17T13-00-00.asc
2025-06-26 18:41:57,902 - core.idw - DEBUG - 所有站点值为零，返回零值栅格
2025-06-26 18:41:57,903 - rasterio.env - DEBUG - Entering env context: <rasterio.env.Env object at 0x0000018E20BCA7C8>
2025-06-26 18:41:57,903 - rasterio.env - DEBUG - Starting outermost env
2025-06-26 18:41:57,903 - rasterio.env - DEBUG - No GDAL environment exists
2025-06-26 18:41:57,904 - rasterio.env - DEBUG - New GDAL environment <rasterio._env.GDALEnv object at 0x0000018E20BCC7C8> created
2025-06-26 18:41:57,905 - rasterio._env - DEBUG - GDAL_DATA found in environment.
2025-06-26 18:41:57,905 - rasterio._env - DEBUG - PROJ_LIB found in environment.
2025-06-26 18:41:57,906 - rasterio._env - DEBUG - Started GDALEnv: self=<rasterio._env.GDALEnv object at 0x0000018E20BCC7C8>.
2025-06-26 18:41:57,906 - rasterio.env - DEBUG - Entered env context: <rasterio.env.Env object at 0x0000018E20BCA7C8>
2025-06-26 18:41:57,906 - rasterio._io - DEBUG - Path: ParsedPath(path='D:/pythondata/jiangyuchazhi/output/IDW/2009-1/rainfall_IDW_2009-04-17T14-00-00.asc', archive=None, scheme=None), mode: w, driver: AAIGrid
2025-06-26 18:41:57,907 - rasterio._base - DEBUG - Nodata success: 1, Nodata value: -9999.000000
2025-06-26 18:41:57,907 - rasterio.env - DEBUG - Exiting env context: <rasterio.env.Env object at 0x0000018E20BCA7C8>
2025-06-26 18:41:57,907 - rasterio.env - DEBUG - Cleared existing <rasterio._env.GDALEnv object at 0x0000018E20BCC7C8> options
2025-06-26 18:41:57,908 - rasterio._env - DEBUG - Stopped GDALEnv <rasterio._env.GDALEnv object at 0x0000018E20BCC7C8>.
2025-06-26 18:41:57,908 - rasterio.env - DEBUG - Exiting outermost env
2025-06-26 18:41:57,908 - rasterio.env - DEBUG - Exited env context: <rasterio.env.Env object at 0x0000018E20BCA7C8>
2025-06-26 18:41:57,909 - rasterio.env - DEBUG - Entering env context: <rasterio.env.Env object at 0x0000018E20BC7208>
2025-06-26 18:41:57,909 - rasterio.env - DEBUG - Starting outermost env
2025-06-26 18:41:57,909 - rasterio.env - DEBUG - No GDAL environment exists
2025-06-26 18:41:57,910 - rasterio.env - DEBUG - New GDAL environment <rasterio._env.GDALEnv object at 0x0000018E20BCA7C8> created
2025-06-26 18:41:57,912 - rasterio._env - DEBUG - GDAL_DATA found in environment.
2025-06-26 18:41:57,912 - rasterio._env - DEBUG - PROJ_LIB found in environment.
2025-06-26 18:41:57,914 - rasterio._env - DEBUG - Started GDALEnv: self=<rasterio._env.GDALEnv object at 0x0000018E20BCA7C8>.
2025-06-26 18:41:57,915 - rasterio.env - DEBUG - Entered env context: <rasterio.env.Env object at 0x0000018E20BC7208>
2025-06-26 18:41:57,920 - rasterio._io - DEBUG - Skipped delete for overwrite. Dataset does not exist: 'D:/pythondata/jiangyuchazhi/output/IDW/2009-1/rainfall_IDW_2009-04-17T14-00-00.asc'
2025-06-26 18:41:57,920 - rasterio._io - DEBUG - Option: ('TILED', b'False')

2025-06-26 18:41:57,921 - rasterio._io - DEBUG - Option: ('COMPRESS', b'None')

2025-06-26 18:41:57,922 - rasterio._env - WARNING - CPLE_NotSupported in driver AAIGrid does not support creation option TILED
2025-06-26 18:41:57,922 - rasterio._env - WARNING - CPLE_NotSupported in driver AAIGrid does not support creation option COMPRESS
2025-06-26 18:41:57,932 - rasterio._io - DEBUG - Created copy from MEM file: D:/pythondata/jiangyuchazhi/output/IDW/2009-1/rainfall_IDW_2009-04-17T14-00-00.asc
2025-06-26 18:41:57,933 - rasterio.env - DEBUG - Exiting env context: <rasterio.env.Env object at 0x0000018E20BC7208>
2025-06-26 18:41:57,933 - rasterio.env - DEBUG - Cleared existing <rasterio._env.GDALEnv object at 0x0000018E20BCA7C8> options
2025-06-26 18:41:57,936 - rasterio._env - DEBUG - Stopped GDALEnv <rasterio._env.GDALEnv object at 0x0000018E20BCA7C8>.
2025-06-26 18:41:57,938 - rasterio.env - DEBUG - Exiting outermost env
2025-06-26 18:41:57,938 - rasterio.env - DEBUG - Exited env context: <rasterio.env.Env object at 0x0000018E20BC7208>
2025-06-26 18:41:57,939 - utils.raster_io - DEBUG - 成功写入栅格文件: D:\pythondata\jiangyuchazhi\output\IDW\2009-1\rainfall_IDW_2009-04-17T14-00-00.asc
2025-06-26 18:41:57,939 - __main__ - INFO - 处理时间步 61/100: 2009-04-17 15:00:00
2025-06-26 18:41:57,985 - core.idw - DEBUG - 所有站点值为零，返回零值栅格
2025-06-26 18:41:57,986 - rasterio.env - DEBUG - Entering env context: <rasterio.env.Env object at 0x0000018E20BAA988>
2025-06-26 18:41:57,987 - rasterio.env - DEBUG - Starting outermost env
2025-06-26 18:41:57,987 - rasterio.env - DEBUG - No GDAL environment exists
2025-06-26 18:41:57,987 - rasterio.env - DEBUG - New GDAL environment <rasterio._env.GDALEnv object at 0x0000018E20BAADC8> created
2025-06-26 18:41:57,989 - rasterio._env - DEBUG - GDAL_DATA found in environment.
2025-06-26 18:41:57,989 - rasterio._env - DEBUG - PROJ_LIB found in environment.
2025-06-26 18:41:57,989 - rasterio._env - DEBUG - Started GDALEnv: self=<rasterio._env.GDALEnv object at 0x0000018E20BAADC8>.
2025-06-26 18:41:57,989 - rasterio.env - DEBUG - Entered env context: <rasterio.env.Env object at 0x0000018E20BAA988>
2025-06-26 18:41:57,990 - rasterio._io - DEBUG - Path: ParsedPath(path='D:/pythondata/jiangyuchazhi/output/IDW/2009-1/rainfall_IDW_2009-04-17T15-00-00.asc', archive=None, scheme=None), mode: w, driver: AAIGrid
2025-06-26 18:41:57,990 - rasterio._base - DEBUG - Nodata success: 1, Nodata value: -9999.000000
2025-06-26 18:41:57,990 - rasterio.env - DEBUG - Exiting env context: <rasterio.env.Env object at 0x0000018E20BAA988>
2025-06-26 18:41:57,991 - rasterio.env - DEBUG - Cleared existing <rasterio._env.GDALEnv object at 0x0000018E20BAADC8> options
2025-06-26 18:41:57,991 - rasterio._env - DEBUG - Stopped GDALEnv <rasterio._env.GDALEnv object at 0x0000018E20BAADC8>.
2025-06-26 18:41:57,991 - rasterio.env - DEBUG - Exiting outermost env
2025-06-26 18:41:57,991 - rasterio.env - DEBUG - Exited env context: <rasterio.env.Env object at 0x0000018E20BAA988>
2025-06-26 18:41:57,992 - rasterio.env - DEBUG - Entering env context: <rasterio.env.Env object at 0x0000018E20B9D988>
2025-06-26 18:41:57,992 - rasterio.env - DEBUG - Starting outermost env
2025-06-26 18:41:57,992 - rasterio.env - DEBUG - No GDAL environment exists
2025-06-26 18:41:57,992 - rasterio.env - DEBUG - New GDAL environment <rasterio._env.GDALEnv object at 0x0000018E20BAA988> created
2025-06-26 18:41:57,995 - rasterio._env - DEBUG - GDAL_DATA found in environment.
2025-06-26 18:41:57,996 - rasterio._env - DEBUG - PROJ_LIB found in environment.
2025-06-26 18:41:57,996 - rasterio._env - DEBUG - Started GDALEnv: self=<rasterio._env.GDALEnv object at 0x0000018E20BAA988>.
2025-06-26 18:41:57,997 - rasterio.env - DEBUG - Entered env context: <rasterio.env.Env object at 0x0000018E20B9D988>
2025-06-26 18:41:57,998 - rasterio._io - DEBUG - Skipped delete for overwrite. Dataset does not exist: 'D:/pythondata/jiangyuchazhi/output/IDW/2009-1/rainfall_IDW_2009-04-17T15-00-00.asc'
2025-06-26 18:41:57,999 - rasterio._io - DEBUG - Option: ('TILED', b'False')

2025-06-26 18:41:57,999 - rasterio._io - DEBUG - Option: ('COMPRESS', b'None')

2025-06-26 18:41:58,000 - rasterio._env - WARNING - CPLE_NotSupported in driver AAIGrid does not support creation option TILED
2025-06-26 18:41:58,000 - rasterio._env - WARNING - CPLE_NotSupported in driver AAIGrid does not support creation option COMPRESS
2025-06-26 18:41:58,010 - rasterio._io - DEBUG - Created copy from MEM file: D:/pythondata/jiangyuchazhi/output/IDW/2009-1/rainfall_IDW_2009-04-17T15-00-00.asc
2025-06-26 18:41:58,010 - rasterio.env - DEBUG - Exiting env context: <rasterio.env.Env object at 0x0000018E20B9D988>
2025-06-26 18:41:58,010 - rasterio.env - DEBUG - Cleared existing <rasterio._env.GDALEnv object at 0x0000018E20BAA988> options
2025-06-26 18:41:58,011 - rasterio._env - DEBUG - Stopped GDALEnv <rasterio._env.GDALEnv object at 0x0000018E20BAA988>.
2025-06-26 18:41:58,011 - rasterio.env - DEBUG - Exiting outermost env
2025-06-26 18:41:58,011 - rasterio.env - DEBUG - Exited env context: <rasterio.env.Env object at 0x0000018E20B9D988>
2025-06-26 18:41:58,011 - utils.raster_io - DEBUG - 成功写入栅格文件: D:\pythondata\jiangyuchazhi\output\IDW\2009-1\rainfall_IDW_2009-04-17T15-00-00.asc
2025-06-26 18:41:58,044 - core.idw - DEBUG - 所有站点值为零，返回零值栅格
2025-06-26 18:41:58,045 - rasterio.env - DEBUG - Entering env context: <rasterio.env.Env object at 0x0000018E20BCC7C8>
2025-06-26 18:41:58,045 - rasterio.env - DEBUG - Starting outermost env
2025-06-26 18:41:58,045 - rasterio.env - DEBUG - No GDAL environment exists
2025-06-26 18:41:58,046 - rasterio.env - DEBUG - New GDAL environment <rasterio._env.GDALEnv object at 0x0000018E20BCC1C8> created
2025-06-26 18:41:58,047 - rasterio._env - DEBUG - GDAL_DATA found in environment.
2025-06-26 18:41:58,047 - rasterio._env - DEBUG - PROJ_LIB found in environment.
2025-06-26 18:41:58,048 - rasterio._env - DEBUG - Started GDALEnv: self=<rasterio._env.GDALEnv object at 0x0000018E20BCC1C8>.
2025-06-26 18:41:58,048 - rasterio.env - DEBUG - Entered env context: <rasterio.env.Env object at 0x0000018E20BCC7C8>
2025-06-26 18:41:58,048 - rasterio._io - DEBUG - Path: ParsedPath(path='D:/pythondata/jiangyuchazhi/output/IDW/2009-1/rainfall_IDW_2009-04-17T16-00-00.asc', archive=None, scheme=None), mode: w, driver: AAIGrid
2025-06-26 18:41:58,049 - rasterio._base - DEBUG - Nodata success: 1, Nodata value: -9999.000000
2025-06-26 18:41:58,049 - rasterio.env - DEBUG - Exiting env context: <rasterio.env.Env object at 0x0000018E20BCC7C8>
2025-06-26 18:41:58,049 - rasterio.env - DEBUG - Cleared existing <rasterio._env.GDALEnv object at 0x0000018E20BCC1C8> options
2025-06-26 18:41:58,050 - rasterio._env - DEBUG - Stopped GDALEnv <rasterio._env.GDALEnv object at 0x0000018E20BCC1C8>.
2025-06-26 18:41:58,050 - rasterio.env - DEBUG - Exiting outermost env
2025-06-26 18:41:58,050 - rasterio.env - DEBUG - Exited env context: <rasterio.env.Env object at 0x0000018E20BCC7C8>
2025-06-26 18:41:58,051 - rasterio.env - DEBUG - Entering env context: <rasterio.env.Env object at 0x0000018E20BCC208>
2025-06-26 18:41:58,051 - rasterio.env - DEBUG - Starting outermost env
2025-06-26 18:41:58,052 - rasterio.env - DEBUG - No GDAL environment exists
2025-06-26 18:41:58,058 - rasterio.env - DEBUG - New GDAL environment <rasterio._env.GDALEnv object at 0x0000018E20BCC7C8> created
2025-06-26 18:41:58,061 - rasterio._env - DEBUG - GDAL_DATA found in environment.
2025-06-26 18:41:58,061 - rasterio._env - DEBUG - PROJ_LIB found in environment.
2025-06-26 18:41:58,062 - rasterio._env - DEBUG - Started GDALEnv: self=<rasterio._env.GDALEnv object at 0x0000018E20BCC7C8>.
2025-06-26 18:41:58,062 - rasterio.env - DEBUG - Entered env context: <rasterio.env.Env object at 0x0000018E20BCC208>
2025-06-26 18:41:58,063 - rasterio._io - DEBUG - Skipped delete for overwrite. Dataset does not exist: 'D:/pythondata/jiangyuchazhi/output/IDW/2009-1/rainfall_IDW_2009-04-17T16-00-00.asc'
2025-06-26 18:41:58,063 - rasterio._io - DEBUG - Option: ('TILED', b'False')

2025-06-26 18:41:58,063 - rasterio._io - DEBUG - Option: ('COMPRESS', b'None')

2025-06-26 18:41:58,064 - rasterio._env - WARNING - CPLE_NotSupported in driver AAIGrid does not support creation option TILED
2025-06-26 18:41:58,064 - rasterio._env - WARNING - CPLE_NotSupported in driver AAIGrid does not support creation option COMPRESS
2025-06-26 18:41:58,082 - rasterio._io - DEBUG - Created copy from MEM file: D:/pythondata/jiangyuchazhi/output/IDW/2009-1/rainfall_IDW_2009-04-17T16-00-00.asc
2025-06-26 18:41:58,090 - rasterio.env - DEBUG - Exiting env context: <rasterio.env.Env object at 0x0000018E20BCC208>
2025-06-26 18:41:58,092 - rasterio.env - DEBUG - Cleared existing <rasterio._env.GDALEnv object at 0x0000018E20BCC7C8> options
2025-06-26 18:41:58,092 - rasterio._env - DEBUG - Stopped GDALEnv <rasterio._env.GDALEnv object at 0x0000018E20BCC7C8>.
2025-06-26 18:41:58,093 - rasterio.env - DEBUG - Exiting outermost env
2025-06-26 18:41:58,093 - rasterio.env - DEBUG - Exited env context: <rasterio.env.Env object at 0x0000018E20BCC208>
2025-06-26 18:41:58,093 - utils.raster_io - DEBUG - 成功写入栅格文件: D:\pythondata\jiangyuchazhi\output\IDW\2009-1\rainfall_IDW_2009-04-17T16-00-00.asc
2025-06-26 18:41:58,125 - core.idw - DEBUG - 所有站点值为零，返回零值栅格
2025-06-26 18:41:58,126 - rasterio.env - DEBUG - Entering env context: <rasterio.env.Env object at 0x0000018E20BC8408>
2025-06-26 18:41:58,126 - rasterio.env - DEBUG - Starting outermost env
2025-06-26 18:41:58,126 - rasterio.env - DEBUG - No GDAL environment exists
2025-06-26 18:41:58,127 - rasterio.env - DEBUG - New GDAL environment <rasterio._env.GDALEnv object at 0x0000018E20B9D988> created
2025-06-26 18:41:58,128 - rasterio._env - DEBUG - GDAL_DATA found in environment.
2025-06-26 18:41:58,128 - rasterio._env - DEBUG - PROJ_LIB found in environment.
2025-06-26 18:41:58,129 - rasterio._env - DEBUG - Started GDALEnv: self=<rasterio._env.GDALEnv object at 0x0000018E20B9D988>.
2025-06-26 18:41:58,129 - rasterio.env - DEBUG - Entered env context: <rasterio.env.Env object at 0x0000018E20BC8408>
2025-06-26 18:41:58,129 - rasterio._io - DEBUG - Path: ParsedPath(path='D:/pythondata/jiangyuchazhi/output/IDW/2009-1/rainfall_IDW_2009-04-17T17-00-00.asc', archive=None, scheme=None), mode: w, driver: AAIGrid
2025-06-26 18:41:58,130 - rasterio._base - DEBUG - Nodata success: 1, Nodata value: -9999.000000
2025-06-26 18:41:58,130 - rasterio.env - DEBUG - Exiting env context: <rasterio.env.Env object at 0x0000018E20BC8408>
2025-06-26 18:41:58,130 - rasterio.env - DEBUG - Cleared existing <rasterio._env.GDALEnv object at 0x0000018E20B9D988> options
2025-06-26 18:41:58,130 - rasterio._env - DEBUG - Stopped GDALEnv <rasterio._env.GDALEnv object at 0x0000018E20B9D988>.
2025-06-26 18:41:58,131 - rasterio.env - DEBUG - Exiting outermost env
2025-06-26 18:41:58,131 - rasterio.env - DEBUG - Exited env context: <rasterio.env.Env object at 0x0000018E20BC8408>
2025-06-26 18:41:58,131 - rasterio.env - DEBUG - Entering env context: <rasterio.env.Env object at 0x0000018E20BC7208>
2025-06-26 18:41:58,132 - rasterio.env - DEBUG - Starting outermost env
2025-06-26 18:41:58,132 - rasterio.env - DEBUG - No GDAL environment exists
2025-06-26 18:41:58,132 - rasterio.env - DEBUG - New GDAL environment <rasterio._env.GDALEnv object at 0x0000018E20BC8408> created
2025-06-26 18:41:58,142 - rasterio._env - DEBUG - GDAL_DATA found in environment.
2025-06-26 18:41:58,143 - rasterio._env - DEBUG - PROJ_LIB found in environment.
2025-06-26 18:41:58,143 - rasterio._env - DEBUG - Started GDALEnv: self=<rasterio._env.GDALEnv object at 0x0000018E20BC8408>.
2025-06-26 18:41:58,143 - rasterio.env - DEBUG - Entered env context: <rasterio.env.Env object at 0x0000018E20BC7208>
2025-06-26 18:41:58,144 - rasterio._io - DEBUG - Skipped delete for overwrite. Dataset does not exist: 'D:/pythondata/jiangyuchazhi/output/IDW/2009-1/rainfall_IDW_2009-04-17T17-00-00.asc'
2025-06-26 18:41:58,144 - rasterio._io - DEBUG - Option: ('TILED', b'False')

2025-06-26 18:41:58,145 - rasterio._io - DEBUG - Option: ('COMPRESS', b'None')

2025-06-26 18:41:58,145 - rasterio._env - WARNING - CPLE_NotSupported in driver AAIGrid does not support creation option TILED
2025-06-26 18:41:58,146 - rasterio._env - WARNING - CPLE_NotSupported in driver AAIGrid does not support creation option COMPRESS
2025-06-26 18:41:58,160 - rasterio._io - DEBUG - Created copy from MEM file: D:/pythondata/jiangyuchazhi/output/IDW/2009-1/rainfall_IDW_2009-04-17T17-00-00.asc
2025-06-26 18:41:58,161 - rasterio.env - DEBUG - Exiting env context: <rasterio.env.Env object at 0x0000018E20BC7208>
2025-06-26 18:41:58,161 - rasterio.env - DEBUG - Cleared existing <rasterio._env.GDALEnv object at 0x0000018E20BC8408> options
2025-06-26 18:41:58,161 - rasterio._env - DEBUG - Stopped GDALEnv <rasterio._env.GDALEnv object at 0x0000018E20BC8408>.
2025-06-26 18:41:58,162 - rasterio.env - DEBUG - Exiting outermost env
2025-06-26 18:41:58,162 - rasterio.env - DEBUG - Exited env context: <rasterio.env.Env object at 0x0000018E20BC7208>
2025-06-26 18:41:58,162 - utils.raster_io - DEBUG - 成功写入栅格文件: D:\pythondata\jiangyuchazhi\output\IDW\2009-1\rainfall_IDW_2009-04-17T17-00-00.asc
2025-06-26 18:41:58,201 - core.idw - DEBUG - 所有站点值为零，返回零值栅格
2025-06-26 18:41:58,210 - rasterio.env - DEBUG - Entering env context: <rasterio.env.Env object at 0x0000018E20BAADC8>
2025-06-26 18:41:58,210 - rasterio.env - DEBUG - Starting outermost env
2025-06-26 18:41:58,210 - rasterio.env - DEBUG - No GDAL environment exists
2025-06-26 18:41:58,210 - rasterio.env - DEBUG - New GDAL environment <rasterio._env.GDALEnv object at 0x0000018E20BAA988> created
2025-06-26 18:41:58,212 - rasterio._env - DEBUG - GDAL_DATA found in environment.
2025-06-26 18:41:58,212 - rasterio._env - DEBUG - PROJ_LIB found in environment.
2025-06-26 18:41:58,213 - rasterio._env - DEBUG - Started GDALEnv: self=<rasterio._env.GDALEnv object at 0x0000018E20BAA988>.
2025-06-26 18:41:58,213 - rasterio.env - DEBUG - Entered env context: <rasterio.env.Env object at 0x0000018E20BAADC8>
2025-06-26 18:41:58,213 - rasterio._io - DEBUG - Path: ParsedPath(path='D:/pythondata/jiangyuchazhi/output/IDW/2009-1/rainfall_IDW_2009-04-17T18-00-00.asc', archive=None, scheme=None), mode: w, driver: AAIGrid
2025-06-26 18:41:58,214 - rasterio._base - DEBUG - Nodata success: 1, Nodata value: -9999.000000
2025-06-26 18:41:58,214 - rasterio.env - DEBUG - Exiting env context: <rasterio.env.Env object at 0x0000018E20BAADC8>
2025-06-26 18:41:58,214 - rasterio.env - DEBUG - Cleared existing <rasterio._env.GDALEnv object at 0x0000018E20BAA988> options
2025-06-26 18:41:58,215 - rasterio._env - DEBUG - Stopped GDALEnv <rasterio._env.GDALEnv object at 0x0000018E20BAA988>.
2025-06-26 18:41:58,215 - rasterio.env - DEBUG - Exiting outermost env
2025-06-26 18:41:58,215 - rasterio.env - DEBUG - Exited env context: <rasterio.env.Env object at 0x0000018E20BAADC8>
2025-06-26 18:41:58,216 - rasterio.env - DEBUG - Entering env context: <rasterio.env.Env object at 0x0000018E20BCC1C8>
2025-06-26 18:41:58,217 - rasterio.env - DEBUG - Starting outermost env
2025-06-26 18:41:58,218 - rasterio.env - DEBUG - No GDAL environment exists
2025-06-26 18:41:58,220 - rasterio.env - DEBUG - New GDAL environment <rasterio._env.GDALEnv object at 0x0000018E20BAADC8> created
2025-06-26 18:41:58,223 - rasterio._env - DEBUG - GDAL_DATA found in environment.
2025-06-26 18:41:58,225 - rasterio._env - DEBUG - PROJ_LIB found in environment.
2025-06-26 18:41:58,225 - rasterio._env - DEBUG - Started GDALEnv: self=<rasterio._env.GDALEnv object at 0x0000018E20BAADC8>.
2025-06-26 18:41:58,226 - rasterio.env - DEBUG - Entered env context: <rasterio.env.Env object at 0x0000018E20BCC1C8>
2025-06-26 18:41:58,227 - rasterio._io - DEBUG - Skipped delete for overwrite. Dataset does not exist: 'D:/pythondata/jiangyuchazhi/output/IDW/2009-1/rainfall_IDW_2009-04-17T18-00-00.asc'
2025-06-26 18:41:58,227 - rasterio._io - DEBUG - Option: ('TILED', b'False')

2025-06-26 18:41:58,227 - rasterio._io - DEBUG - Option: ('COMPRESS', b'None')

2025-06-26 18:41:58,229 - rasterio._env - WARNING - CPLE_NotSupported in driver AAIGrid does not support creation option TILED
2025-06-26 18:41:58,229 - rasterio._env - WARNING - CPLE_NotSupported in driver AAIGrid does not support creation option COMPRESS
2025-06-26 18:41:58,246 - rasterio._io - DEBUG - Created copy from MEM file: D:/pythondata/jiangyuchazhi/output/IDW/2009-1/rainfall_IDW_2009-04-17T18-00-00.asc
2025-06-26 18:41:58,246 - rasterio.env - DEBUG - Exiting env context: <rasterio.env.Env object at 0x0000018E20BCC1C8>
2025-06-26 18:41:58,246 - rasterio.env - DEBUG - Cleared existing <rasterio._env.GDALEnv object at 0x0000018E20BAADC8> options
2025-06-26 18:41:58,247 - rasterio._env - DEBUG - Stopped GDALEnv <rasterio._env.GDALEnv object at 0x0000018E20BAADC8>.
2025-06-26 18:41:58,247 - rasterio.env - DEBUG - Exiting outermost env
2025-06-26 18:41:58,247 - rasterio.env - DEBUG - Exited env context: <rasterio.env.Env object at 0x0000018E20BCC1C8>
2025-06-26 18:41:58,248 - utils.raster_io - DEBUG - 成功写入栅格文件: D:\pythondata\jiangyuchazhi\output\IDW\2009-1\rainfall_IDW_2009-04-17T18-00-00.asc
2025-06-26 18:41:58,280 - core.idw - DEBUG - 所有站点值为零，返回零值栅格
2025-06-26 18:41:58,281 - rasterio.env - DEBUG - Entering env context: <rasterio.env.Env object at 0x0000018E20BCB208>
2025-06-26 18:41:58,281 - rasterio.env - DEBUG - Starting outermost env
2025-06-26 18:41:58,281 - rasterio.env - DEBUG - No GDAL environment exists
2025-06-26 18:41:58,282 - rasterio.env - DEBUG - New GDAL environment <rasterio._env.GDALEnv object at 0x0000018E20BC8408> created
2025-06-26 18:41:58,284 - rasterio._env - DEBUG - GDAL_DATA found in environment.
2025-06-26 18:41:58,284 - rasterio._env - DEBUG - PROJ_LIB found in environment.
2025-06-26 18:41:58,285 - rasterio._env - DEBUG - Started GDALEnv: self=<rasterio._env.GDALEnv object at 0x0000018E20BC8408>.
2025-06-26 18:41:58,286 - rasterio.env - DEBUG - Entered env context: <rasterio.env.Env object at 0x0000018E20BCB208>
2025-06-26 18:41:58,287 - rasterio._io - DEBUG - Path: ParsedPath(path='D:/pythondata/jiangyuchazhi/output/IDW/2009-1/rainfall_IDW_2009-04-17T19-00-00.asc', archive=None, scheme=None), mode: w, driver: AAIGrid
2025-06-26 18:41:58,287 - rasterio._base - DEBUG - Nodata success: 1, Nodata value: -9999.000000
2025-06-26 18:41:58,288 - rasterio.env - DEBUG - Exiting env context: <rasterio.env.Env object at 0x0000018E20BCB208>
2025-06-26 18:41:58,288 - rasterio.env - DEBUG - Cleared existing <rasterio._env.GDALEnv object at 0x0000018E20BC8408> options
2025-06-26 18:41:58,289 - rasterio._env - DEBUG - Stopped GDALEnv <rasterio._env.GDALEnv object at 0x0000018E20BC8408>.
2025-06-26 18:41:58,289 - rasterio.env - DEBUG - Exiting outermost env
2025-06-26 18:41:58,290 - rasterio.env - DEBUG - Exited env context: <rasterio.env.Env object at 0x0000018E20BCB208>
2025-06-26 18:41:58,292 - rasterio.env - DEBUG - Entering env context: <rasterio.env.Env object at 0x0000018E20BCA7C8>
2025-06-26 18:41:58,293 - rasterio.env - DEBUG - Starting outermost env
2025-06-26 18:41:58,293 - rasterio.env - DEBUG - No GDAL environment exists
2025-06-26 18:41:58,293 - rasterio.env - DEBUG - New GDAL environment <rasterio._env.GDALEnv object at 0x0000018E20BCB208> created
2025-06-26 18:41:58,295 - rasterio._env - DEBUG - GDAL_DATA found in environment.
2025-06-26 18:41:58,295 - rasterio._env - DEBUG - PROJ_LIB found in environment.
2025-06-26 18:41:58,296 - rasterio._env - DEBUG - Started GDALEnv: self=<rasterio._env.GDALEnv object at 0x0000018E20BCB208>.
2025-06-26 18:41:58,296 - rasterio.env - DEBUG - Entered env context: <rasterio.env.Env object at 0x0000018E20BCA7C8>
2025-06-26 18:41:58,297 - rasterio._io - DEBUG - Skipped delete for overwrite. Dataset does not exist: 'D:/pythondata/jiangyuchazhi/output/IDW/2009-1/rainfall_IDW_2009-04-17T19-00-00.asc'
2025-06-26 18:41:58,297 - rasterio._io - DEBUG - Option: ('TILED', b'False')

2025-06-26 18:41:58,298 - rasterio._io - DEBUG - Option: ('COMPRESS', b'None')

2025-06-26 18:41:58,299 - rasterio._env - WARNING - CPLE_NotSupported in driver AAIGrid does not support creation option TILED
2025-06-26 18:41:58,300 - rasterio._env - WARNING - CPLE_NotSupported in driver AAIGrid does not support creation option COMPRESS
2025-06-26 18:41:58,314 - rasterio._io - DEBUG - Created copy from MEM file: D:/pythondata/jiangyuchazhi/output/IDW/2009-1/rainfall_IDW_2009-04-17T19-00-00.asc
2025-06-26 18:41:58,315 - rasterio.env - DEBUG - Exiting env context: <rasterio.env.Env object at 0x0000018E20BCA7C8>
2025-06-26 18:41:58,316 - rasterio.env - DEBUG - Cleared existing <rasterio._env.GDALEnv object at 0x0000018E20BCB208> options
2025-06-26 18:41:58,316 - rasterio._env - DEBUG - Stopped GDALEnv <rasterio._env.GDALEnv object at 0x0000018E20BCB208>.
2025-06-26 18:41:58,316 - rasterio.env - DEBUG - Exiting outermost env
2025-06-26 18:41:58,316 - rasterio.env - DEBUG - Exited env context: <rasterio.env.Env object at 0x0000018E20BCA7C8>
2025-06-26 18:41:58,317 - utils.raster_io - DEBUG - 成功写入栅格文件: D:\pythondata\jiangyuchazhi\output\IDW\2009-1\rainfall_IDW_2009-04-17T19-00-00.asc
2025-06-26 18:41:58,363 - core.idw - DEBUG - 所有站点值为零，返回零值栅格
2025-06-26 18:41:58,363 - rasterio.env - DEBUG - Entering env context: <rasterio.env.Env object at 0x0000018E20BCC7C8>
2025-06-26 18:41:58,363 - rasterio.env - DEBUG - Starting outermost env
2025-06-26 18:41:58,364 - rasterio.env - DEBUG - No GDAL environment exists
2025-06-26 18:41:58,364 - rasterio.env - DEBUG - New GDAL environment <rasterio._env.GDALEnv object at 0x0000018E20BCC1C8> created
2025-06-26 18:41:58,366 - rasterio._env - DEBUG - GDAL_DATA found in environment.
2025-06-26 18:41:58,366 - rasterio._env - DEBUG - PROJ_LIB found in environment.
2025-06-26 18:41:58,367 - rasterio._env - DEBUG - Started GDALEnv: self=<rasterio._env.GDALEnv object at 0x0000018E20BCC1C8>.
2025-06-26 18:41:58,367 - rasterio.env - DEBUG - Entered env context: <rasterio.env.Env object at 0x0000018E20BCC7C8>
2025-06-26 18:41:58,368 - rasterio._io - DEBUG - Path: ParsedPath(path='D:/pythondata/jiangyuchazhi/output/IDW/2009-1/rainfall_IDW_2009-04-17T20-00-00.asc', archive=None, scheme=None), mode: w, driver: AAIGrid
2025-06-26 18:41:58,368 - rasterio._base - DEBUG - Nodata success: 1, Nodata value: -9999.000000
2025-06-26 18:41:58,369 - rasterio.env - DEBUG - Exiting env context: <rasterio.env.Env object at 0x0000018E20BCC7C8>
2025-06-26 18:41:58,370 - rasterio.env - DEBUG - Cleared existing <rasterio._env.GDALEnv object at 0x0000018E20BCC1C8> options
2025-06-26 18:41:58,370 - rasterio._env - DEBUG - Stopped GDALEnv <rasterio._env.GDALEnv object at 0x0000018E20BCC1C8>.
2025-06-26 18:41:58,370 - rasterio.env - DEBUG - Exiting outermost env
2025-06-26 18:41:58,370 - rasterio.env - DEBUG - Exited env context: <rasterio.env.Env object at 0x0000018E20BCC7C8>
2025-06-26 18:41:58,371 - rasterio.env - DEBUG - Entering env context: <rasterio.env.Env object at 0x0000018E20BC7208>
2025-06-26 18:41:58,371 - rasterio.env - DEBUG - Starting outermost env
2025-06-26 18:41:58,371 - rasterio.env - DEBUG - No GDAL environment exists
2025-06-26 18:41:58,371 - rasterio.env - DEBUG - New GDAL environment <rasterio._env.GDALEnv object at 0x0000018E20BCC7C8> created
2025-06-26 18:41:58,373 - rasterio._env - DEBUG - GDAL_DATA found in environment.
2025-06-26 18:41:58,373 - rasterio._env - DEBUG - PROJ_LIB found in environment.
2025-06-26 18:41:58,374 - rasterio._env - DEBUG - Started GDALEnv: self=<rasterio._env.GDALEnv object at 0x0000018E20BCC7C8>.
2025-06-26 18:41:58,374 - rasterio.env - DEBUG - Entered env context: <rasterio.env.Env object at 0x0000018E20BC7208>
2025-06-26 18:41:58,375 - rasterio._io - DEBUG - Skipped delete for overwrite. Dataset does not exist: 'D:/pythondata/jiangyuchazhi/output/IDW/2009-1/rainfall_IDW_2009-04-17T20-00-00.asc'
2025-06-26 18:41:58,376 - rasterio._io - DEBUG - Option: ('TILED', b'False')

2025-06-26 18:41:58,376 - rasterio._io - DEBUG - Option: ('COMPRESS', b'None')

2025-06-26 18:41:58,377 - rasterio._env - WARNING - CPLE_NotSupported in driver AAIGrid does not support creation option TILED
2025-06-26 18:41:58,377 - rasterio._env - WARNING - CPLE_NotSupported in driver AAIGrid does not support creation option COMPRESS
2025-06-26 18:41:58,390 - rasterio._io - DEBUG - Created copy from MEM file: D:/pythondata/jiangyuchazhi/output/IDW/2009-1/rainfall_IDW_2009-04-17T20-00-00.asc
2025-06-26 18:41:58,391 - rasterio.env - DEBUG - Exiting env context: <rasterio.env.Env object at 0x0000018E20BC7208>
2025-06-26 18:41:58,391 - rasterio.env - DEBUG - Cleared existing <rasterio._env.GDALEnv object at 0x0000018E20BCC7C8> options
2025-06-26 18:41:58,392 - rasterio._env - DEBUG - Stopped GDALEnv <rasterio._env.GDALEnv object at 0x0000018E20BCC7C8>.
2025-06-26 18:41:58,392 - rasterio.env - DEBUG - Exiting outermost env
2025-06-26 18:41:58,392 - rasterio.env - DEBUG - Exited env context: <rasterio.env.Env object at 0x0000018E20BC7208>
2025-06-26 18:41:58,393 - utils.raster_io - DEBUG - 成功写入栅格文件: D:\pythondata\jiangyuchazhi\output\IDW\2009-1\rainfall_IDW_2009-04-17T20-00-00.asc
2025-06-26 18:41:58,424 - core.idw - DEBUG - 所有站点值为零，返回零值栅格
2025-06-26 18:41:58,427 - rasterio.env - DEBUG - Entering env context: <rasterio.env.Env object at 0x0000018E20BCC208>
2025-06-26 18:41:58,427 - rasterio.env - DEBUG - Starting outermost env
2025-06-26 18:41:58,427 - rasterio.env - DEBUG - No GDAL environment exists
2025-06-26 18:41:58,427 - rasterio.env - DEBUG - New GDAL environment <rasterio._env.GDALEnv object at 0x0000018E20BCB208> created
2025-06-26 18:41:58,429 - rasterio._env - DEBUG - GDAL_DATA found in environment.
2025-06-26 18:41:58,429 - rasterio._env - DEBUG - PROJ_LIB found in environment.
2025-06-26 18:41:58,429 - rasterio._env - DEBUG - Started GDALEnv: self=<rasterio._env.GDALEnv object at 0x0000018E20BCB208>.
2025-06-26 18:41:58,429 - rasterio.env - DEBUG - Entered env context: <rasterio.env.Env object at 0x0000018E20BCC208>
2025-06-26 18:41:58,430 - rasterio._io - DEBUG - Path: ParsedPath(path='D:/pythondata/jiangyuchazhi/output/IDW/2009-1/rainfall_IDW_2009-04-17T21-00-00.asc', archive=None, scheme=None), mode: w, driver: AAIGrid
2025-06-26 18:41:58,430 - rasterio._base - DEBUG - Nodata success: 1, Nodata value: -9999.000000
2025-06-26 18:41:58,431 - rasterio.env - DEBUG - Exiting env context: <rasterio.env.Env object at 0x0000018E20BCC208>
2025-06-26 18:41:58,432 - rasterio.env - DEBUG - Cleared existing <rasterio._env.GDALEnv object at 0x0000018E20BCB208> options
2025-06-26 18:41:58,432 - rasterio._env - DEBUG - Stopped GDALEnv <rasterio._env.GDALEnv object at 0x0000018E20BCB208>.
2025-06-26 18:41:58,436 - rasterio.env - DEBUG - Exiting outermost env
2025-06-26 18:41:58,436 - rasterio.env - DEBUG - Exited env context: <rasterio.env.Env object at 0x0000018E20BCC208>
2025-06-26 18:41:58,439 - rasterio.env - DEBUG - Entering env context: <rasterio.env.Env object at 0x0000018E20BAADC8>
2025-06-26 18:41:58,440 - rasterio.env - DEBUG - Starting outermost env
2025-06-26 18:41:58,440 - rasterio.env - DEBUG - No GDAL environment exists
2025-06-26 18:41:58,441 - rasterio.env - DEBUG - New GDAL environment <rasterio._env.GDALEnv object at 0x0000018E20BCC208> created
2025-06-26 18:41:58,443 - rasterio._env - DEBUG - GDAL_DATA found in environment.
2025-06-26 18:41:58,444 - rasterio._env - DEBUG - PROJ_LIB found in environment.
2025-06-26 18:41:58,444 - rasterio._env - DEBUG - Started GDALEnv: self=<rasterio._env.GDALEnv object at 0x0000018E20BCC208>.
2025-06-26 18:41:58,444 - rasterio.env - DEBUG - Entered env context: <rasterio.env.Env object at 0x0000018E20BAADC8>
2025-06-26 18:41:58,445 - rasterio._io - DEBUG - Skipped delete for overwrite. Dataset does not exist: 'D:/pythondata/jiangyuchazhi/output/IDW/2009-1/rainfall_IDW_2009-04-17T21-00-00.asc'
2025-06-26 18:41:58,445 - rasterio._io - DEBUG - Option: ('TILED', b'False')

2025-06-26 18:41:58,446 - rasterio._io - DEBUG - Option: ('COMPRESS', b'None')

2025-06-26 18:41:58,446 - rasterio._env - WARNING - CPLE_NotSupported in driver AAIGrid does not support creation option TILED
2025-06-26 18:41:58,447 - rasterio._env - WARNING - CPLE_NotSupported in driver AAIGrid does not support creation option COMPRESS
2025-06-26 18:41:58,460 - rasterio._io - DEBUG - Created copy from MEM file: D:/pythondata/jiangyuchazhi/output/IDW/2009-1/rainfall_IDW_2009-04-17T21-00-00.asc
2025-06-26 18:41:58,460 - rasterio.env - DEBUG - Exiting env context: <rasterio.env.Env object at 0x0000018E20BAADC8>
2025-06-26 18:41:58,461 - rasterio.env - DEBUG - Cleared existing <rasterio._env.GDALEnv object at 0x0000018E20BCC208> options
2025-06-26 18:41:58,461 - rasterio._env - DEBUG - Stopped GDALEnv <rasterio._env.GDALEnv object at 0x0000018E20BCC208>.
2025-06-26 18:41:58,461 - rasterio.env - DEBUG - Exiting outermost env
2025-06-26 18:41:58,462 - rasterio.env - DEBUG - Exited env context: <rasterio.env.Env object at 0x0000018E20BAADC8>
2025-06-26 18:41:58,462 - utils.raster_io - DEBUG - 成功写入栅格文件: D:\pythondata\jiangyuchazhi\output\IDW\2009-1\rainfall_IDW_2009-04-17T21-00-00.asc
2025-06-26 18:41:58,495 - __main__ - WARNING - 生成时间步 2009-04-17 22:00:00 的栅格失败: boolean index did not match indexed array along dimension 0; dimension is 34 but corresponding boolean dimension is 46
2025-06-26 18:41:58,533 - __main__ - WARNING - 生成时间步 2009-04-17 23:00:00 的栅格失败: boolean index did not match indexed array along dimension 0; dimension is 34 but corresponding boolean dimension is 46
2025-06-26 18:41:58,564 - __main__ - WARNING - 生成时间步 2009-04-18 00:00:00 的栅格失败: boolean index did not match indexed array along dimension 0; dimension is 34 but corresponding boolean dimension is 46
2025-06-26 18:41:58,565 - __main__ - INFO - 处理时间步 71/100: 2009-04-18 01:00:00
2025-06-26 18:41:58,591 - __main__ - WARNING - 生成时间步 2009-04-18 01:00:00 的栅格失败: boolean index did not match indexed array along dimension 0; dimension is 34 but corresponding boolean dimension is 46
2025-06-26 18:41:58,617 - __main__ - WARNING - 生成时间步 2009-04-18 02:00:00 的栅格失败: boolean index did not match indexed array along dimension 0; dimension is 34 but corresponding boolean dimension is 46
2025-06-26 18:41:58,646 - __main__ - WARNING - 生成时间步 2009-04-18 03:00:00 的栅格失败: boolean index did not match indexed array along dimension 0; dimension is 34 but corresponding boolean dimension is 46
2025-06-26 18:41:58,672 - __main__ - WARNING - 生成时间步 2009-04-18 04:00:00 的栅格失败: boolean index did not match indexed array along dimension 0; dimension is 34 but corresponding boolean dimension is 46
2025-06-26 18:41:58,701 - __main__ - WARNING - 生成时间步 2009-04-18 05:00:00 的栅格失败: boolean index did not match indexed array along dimension 0; dimension is 34 but corresponding boolean dimension is 46
2025-06-26 18:41:58,735 - __main__ - WARNING - 生成时间步 2009-04-18 06:00:00 的栅格失败: boolean index did not match indexed array along dimension 0; dimension is 34 but corresponding boolean dimension is 46
2025-06-26 18:41:58,760 - __main__ - WARNING - 生成时间步 2009-04-18 07:00:00 的栅格失败: boolean index did not match indexed array along dimension 0; dimension is 34 but corresponding boolean dimension is 46
2025-06-26 18:41:58,785 - __main__ - WARNING - 生成时间步 2009-04-18 08:00:00 的栅格失败: boolean index did not match indexed array along dimension 0; dimension is 34 but corresponding boolean dimension is 46
2025-06-26 18:41:58,812 - __main__ - WARNING - 生成时间步 2009-04-18 09:00:00 的栅格失败: boolean index did not match indexed array along dimension 0; dimension is 34 but corresponding boolean dimension is 46
2025-06-26 18:41:58,838 - __main__ - WARNING - 生成时间步 2009-04-18 10:00:00 的栅格失败: boolean index did not match indexed array along dimension 0; dimension is 34 but corresponding boolean dimension is 46
2025-06-26 18:41:58,839 - __main__ - INFO - 处理时间步 81/100: 2009-04-18 11:00:00
2025-06-26 18:41:58,864 - __main__ - WARNING - 生成时间步 2009-04-18 11:00:00 的栅格失败: boolean index did not match indexed array along dimension 0; dimension is 34 but corresponding boolean dimension is 46
2025-06-26 18:41:58,901 - __main__ - WARNING - 生成时间步 2009-04-18 12:00:00 的栅格失败: boolean index did not match indexed array along dimension 0; dimension is 34 but corresponding boolean dimension is 46
2025-06-26 18:41:58,924 - __main__ - WARNING - 生成时间步 2009-04-18 13:00:00 的栅格失败: boolean index did not match indexed array along dimension 0; dimension is 34 but corresponding boolean dimension is 46
2025-06-26 18:41:59,010 - __main__ - WARNING - 生成时间步 2009-04-18 14:00:00 的栅格失败: boolean index did not match indexed array along dimension 0; dimension is 34 but corresponding boolean dimension is 46
2025-06-26 18:41:59,043 - __main__ - WARNING - 生成时间步 2009-04-18 15:00:00 的栅格失败: boolean index did not match indexed array along dimension 0; dimension is 34 but corresponding boolean dimension is 46
2025-06-26 18:41:59,075 - __main__ - WARNING - 生成时间步 2009-04-18 16:00:00 的栅格失败: boolean index did not match indexed array along dimension 0; dimension is 34 but corresponding boolean dimension is 46
2025-06-26 18:41:59,101 - __main__ - WARNING - 生成时间步 2009-04-18 17:00:00 的栅格失败: boolean index did not match indexed array along dimension 0; dimension is 34 but corresponding boolean dimension is 46
2025-06-26 18:41:59,124 - __main__ - WARNING - 生成时间步 2009-04-18 18:00:00 的栅格失败: boolean index did not match indexed array along dimension 0; dimension is 34 but corresponding boolean dimension is 46
2025-06-26 18:41:59,149 - __main__ - WARNING - 生成时间步 2009-04-18 19:00:00 的栅格失败: boolean index did not match indexed array along dimension 0; dimension is 34 but corresponding boolean dimension is 46
2025-06-26 18:41:59,176 - __main__ - WARNING - 生成时间步 2009-04-18 20:00:00 的栅格失败: boolean index did not match indexed array along dimension 0; dimension is 34 but corresponding boolean dimension is 46
2025-06-26 18:41:59,176 - __main__ - INFO - 处理时间步 91/100: 2009-04-18 21:00:00
2025-06-26 18:41:59,202 - __main__ - WARNING - 生成时间步 2009-04-18 21:00:00 的栅格失败: boolean index did not match indexed array along dimension 0; dimension is 34 but corresponding boolean dimension is 46
2025-06-26 18:41:59,231 - __main__ - WARNING - 生成时间步 2009-04-18 22:00:00 的栅格失败: boolean index did not match indexed array along dimension 0; dimension is 34 but corresponding boolean dimension is 46
2025-06-26 18:41:59,263 - __main__ - WARNING - 生成时间步 2009-04-18 23:00:00 的栅格失败: boolean index did not match indexed array along dimension 0; dimension is 34 but corresponding boolean dimension is 46
2025-06-26 18:41:59,287 - __main__ - WARNING - 生成时间步 2009-04-19 00:00:00 的栅格失败: boolean index did not match indexed array along dimension 0; dimension is 34 but corresponding boolean dimension is 46
2025-06-26 18:41:59,324 - __main__ - WARNING - 生成时间步 2009-04-19 01:00:00 的栅格失败: boolean index did not match indexed array along dimension 0; dimension is 34 but corresponding boolean dimension is 46
2025-06-26 18:41:59,358 - __main__ - WARNING - 生成时间步 2009-04-19 02:00:00 的栅格失败: boolean index did not match indexed array along dimension 0; dimension is 34 but corresponding boolean dimension is 46
2025-06-26 18:41:59,399 - __main__ - WARNING - 生成时间步 2009-04-19 03:00:00 的栅格失败: boolean index did not match indexed array along dimension 0; dimension is 34 but corresponding boolean dimension is 46
2025-06-26 18:41:59,427 - __main__ - WARNING - 生成时间步 2009-04-19 04:00:00 的栅格失败: boolean index did not match indexed array along dimension 0; dimension is 34 but corresponding boolean dimension is 46
2025-06-26 18:41:59,451 - __main__ - WARNING - 生成时间步 2009-04-19 05:00:00 的栅格失败: boolean index did not match indexed array along dimension 0; dimension is 34 but corresponding boolean dimension is 46
2025-06-26 18:41:59,486 - __main__ - WARNING - 生成时间步 2009-04-19 06:00:00 的栅格失败: boolean index did not match indexed array along dimension 0; dimension is 34 but corresponding boolean dimension is 46
2025-06-26 18:41:59,486 - __main__ - INFO - 栅格输出完成，保存到: D:\pythondata\jiangyuchazhi\output\IDW\2009-1
2025-06-26 18:41:59,487 - __main__ - INFO - IDW 方法处理 2009-1 完成
2025-06-26 18:41:59,495 - evaluation.validator - INFO - 验证结果已保存到: D:\pythondata\jiangyuchazhi\output\evaluation_summary.csv
2025-06-26 18:41:59,495 - __main__ - INFO - ============================================================
2025-06-26 18:41:59,496 - __main__ - INFO - 所有任务完成！
2025-06-26 18:41:59,496 - __main__ - INFO - 评估摘要: D:\pythondata\jiangyuchazhi\output\evaluation_summary.csv
2025-06-26 18:41:59,496 - __main__ - INFO - 详细报告: None
2025-06-26 18:41:59,496 - __main__ - INFO - ============================================================
