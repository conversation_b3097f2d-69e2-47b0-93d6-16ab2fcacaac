# -*- coding: utf-8 -*-
"""
降雨空间插值系统主程序
支持多种运行模式和批处理功能
"""

import os
import sys
import argparse
import yaml
import logging
import time
import numpy as np
from pathlib import Path
from typing import Dict, List, Optional

# 添加当前目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from utils.data_loader import DataLoader
from utils.raster_io import Raster<PERSON>
from core.idw import IDWInterpolator
from core.kriging import KrigingInterpolator
from core.oi import OIInterpolator
from core.prism import PRISMInterpolator
from optimization.sceua_optimizer import SCEUAOptimizer
from evaluation.validator import ModelValidator


def setup_logging(config: Dict) -> None:
    """设置日志配置"""
    log_level = logging.DEBUG if config['run_settings'].get('debug_mode', False) else logging.INFO
    
    logging.basicConfig(
        level=log_level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('interpolation.log', encoding='utf-8')
        ]
    )


def load_config(config_path: str) -> Dict:
    """加载配置文件"""
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        return config
    except Exception as e:
        print(f"加载配置文件失败: {e}")
        sys.exit(1)


def create_interpolator(method: str, config: Dict):
    """创建插值器"""
    if method == 'IDW':
        return IDWInterpolator(config)
    elif method == 'Kriging':
        return KrigingInterpolator(config)
    elif method == 'OI':
        return OIInterpolator(config)
    elif method == 'PRISM':
        return PRISMInterpolator(config)
    else:
        raise ValueError(f"不支持的插值方法: {method}")


def run_single_method_single_flood(method: str, flood_event: str, config: Dict) -> Dict:
    """运行单一方法于单一洪水事件"""
    logger = logging.getLogger(__name__)
    logger.info(f"开始运行 {method} 方法处理洪水事件 {flood_event}")
    
    # 初始化组件
    data_loader = DataLoader(config)
    raster_io = RasterIO(config)
    validator = ModelValidator(config)
    optimizer = SCEUAOptimizer(config)
    
    try:
        # 加载数据
        logger.info("加载基础数据...")
        stations_data = data_loader.load_stations()
        delaunay_data = data_loader.load_delaunay_molan()
        terrain_data = data_loader.load_terrain_data()
        rainfall_data = data_loader.load_rainfall_data(flood_event)
        
        # 获取网格坐标
        grid_x, grid_y = data_loader.get_grid_coordinates()
        mask = terrain_data['mask'][0]
        
        # 创建插值器
        interpolator = create_interpolator(method, config)
        
        # 参数优化（如果启用）
        if optimizer.is_optimization_enabled(method):
            logger.info(f"开始优化 {method} 参数...")
            
            # 准备优化数据
            event_delaunay = delaunay_data[delaunay_data.get('flood_event', flood_event) == flood_event] if 'flood_event' in delaunay_data.columns else delaunay_data
            
            if not event_delaunay.empty:
                # 获取一个代表性的时间步进行优化
                sample_time = rainfall_data.index[len(rainfall_data) // 2]
                
                # 准备优化数据
                target_stations = event_delaunay['target_station'].unique()[:5]  # 限制数量以加快优化
                
                station_coords_list = []
                station_values_list = []
                target_coords_list = []
                target_values_list = []
                
                for target_station in target_stations:
                    target_rows = event_delaunay[event_delaunay['target_station'] == target_station]
                    if target_rows.empty:
                        continue
                    
                    # 获取插值站点
                    interp_stations = []
                    for _, row in target_rows.iterrows():
                        stations_list = row['interp_stations_list']
                        if isinstance(stations_list, list):
                            interp_stations.extend(stations_list)
                    
                    interp_stations = list(set(interp_stations))
                    available_stations = set(rainfall_data.columns)
                    valid_interp_stations = [s for s in interp_stations if s in available_stations]
                    
                    if len(valid_interp_stations) < 2 or target_station not in available_stations:
                        continue
                    
                    # 获取坐标和数据
                    interp_coords = get_station_coordinates(valid_interp_stations, stations_data)
                    target_coord = get_station_coordinates([target_station], stations_data)
                    
                    if interp_coords is None or target_coord is None:
                        continue
                    
                    interp_values = rainfall_data.loc[sample_time, valid_interp_stations].values
                    target_value = rainfall_data.loc[sample_time, target_station]
                    
                    if not (np.isnan(target_value) or np.any(np.isnan(interp_values))):
                        station_coords_list.append(interp_coords)
                        station_values_list.append(interp_values)
                        target_coords_list.append(target_coord)
                        target_values_list.append([target_value])
                
                if station_coords_list:
                    # 合并数据进行优化
                    all_station_coords = np.vstack(station_coords_list)
                    all_station_values = np.concatenate(station_values_list)
                    all_target_coords = np.vstack(target_coords_list)
                    all_target_values = np.concatenate(target_values_list)
                    
                    optimal_params, optimal_value = optimizer.optimize_method_parameters(
                        method, interpolator, all_station_coords, all_station_values,
                        all_target_coords, all_target_values, grid_x, grid_y, mask
                    )
                    
                    if optimal_params:
                        interpolator.set_optimized_parameters(optimal_params)
                        logger.info(f"参数优化完成，最优值: {optimal_value:.4f}")
        
        # 执行交叉验证
        logger.info("执行交叉验证...")
        validation_result = validator.perform_loocv(
            interpolator, flood_event, delaunay_data, stations_data,
            rainfall_data, grid_x, grid_y, mask, terrain_data
        )
        
        # 生成栅格输出（如果启用）
        if config['run_settings']['generate_raster_output']:
            logger.info("生成栅格输出...")
            output_dir = Path(config['project_paths']['base_dir']) / \
                        config['project_paths']['output_dir_template'].format(
                            method=method, flood_event=flood_event)
            
            generate_raster_outputs(interpolator, flood_event, delaunay_data, 
                                  stations_data, rainfall_data, grid_x, grid_y, 
                                  mask, terrain_data, output_dir, raster_io, config)
        
        logger.info(f"{method} 方法处理 {flood_event} 完成")
        return validation_result
        
    except Exception as e:
        logger.error(f"运行失败: {e}")
        raise


def get_station_coordinates(station_ids: List[str], stations_data) -> Optional:
    """获取站点坐标的辅助函数"""
    import numpy as np
    
    coords = []
    for station_id in station_ids:
        station_row = stations_data[stations_data['站点编号'] == station_id]
        if station_row.empty:
            continue
        
        lon = station_row.iloc[0]['经度']
        lat = station_row.iloc[0]['纬度']
        
        if np.isnan(lon) or np.isnan(lat):
            continue
        
        coords.append([lon, lat])
    
    return np.array(coords) if coords else None


def generate_raster_outputs(interpolator, flood_event: str, delaunay_data, 
                          stations_data, rainfall_data, grid_x, grid_y, 
                          mask, terrain_data, output_dir: Path, raster_io: RasterIO, 
                          config: Dict) -> None:
    """生成栅格输出"""
    import numpy as np
    
    logger = logging.getLogger(__name__)
    
    # 设置地形数据（如果是PRISM）
    if hasattr(interpolator, 'set_terrain_data') and terrain_data is not None:
        interpolator.set_terrain_data(
            terrain_data['dem'][0], terrain_data['slope'][0], 
            terrain_data['aspect'][0], grid_x, grid_y
        )
    
    # 获取栅格配置
    mask_data, mask_profile = terrain_data['mask']
    raster_profile = raster_io.get_raster_profile_from_mask(mask_data, mask_profile)
    
    # 限制输出时间步数以避免过多文件
    max_timesteps = config['data_processing'].get('batch_size', 100)
    timesteps = rainfall_data.index[:max_timesteps]
    
    logger.info(f"生成 {len(timesteps)} 个时间步的栅格输出")
    
    for i, timestamp in enumerate(timesteps):
        try:
            if i % 10 == 0:
                logger.info(f"处理时间步 {i+1}/{len(timesteps)}: {timestamp}")
            
            # 获取所有可用站点的当前时刻数据
            current_rainfall = rainfall_data.loc[timestamp]
            valid_stations = current_rainfall.dropna().index.tolist()
            
            if len(valid_stations) < 2:
                continue
            
            # 获取站点坐标和值
            station_coords = get_station_coordinates(valid_stations, stations_data)
            if station_coords is None:
                continue
            
            station_values = current_rainfall[valid_stations].values
            
            # 执行插值
            interpolated_field = interpolator.interpolate(
                station_coords, station_values, grid_x, grid_y, mask
            )
            
            # 应用掩膜
            interpolated_field = raster_io.apply_mask(interpolated_field, mask_data)
            
            # 生成文件名
            time_str = timestamp.strftime(config['output_settings']['time_format'])
            filename = raster_io.create_output_filename(
                interpolator.get_method_name(), flood_event, time_str
            )
            
            # 保存栅格
            output_path = output_dir / filename
            raster_io.write_asc(interpolated_field, output_path, raster_profile)
            
        except Exception as e:
            logger.warning(f"生成时间步 {timestamp} 的栅格失败: {e}")
            continue
    
    logger.info(f"栅格输出完成，保存到: {output_dir}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='降雨空间插值系统')
    parser.add_argument('--method', type=str, default='all',
                       choices=['IDW', 'Kriging', 'OI', 'PRISM', 'all'],
                       help='插值方法')
    parser.add_argument('--flood', type=str, default='all',
                       help='洪水事件（如 2009-1）或 all')
    parser.add_argument('--config', type=str, 
                       default='config/config.yml',
                       help='配置文件路径')
    
    args = parser.parse_args()
    
    # 加载配置
    config_path = Path(__file__).parent / args.config
    config = load_config(str(config_path))
    
    # 设置日志
    setup_logging(config)
    logger = logging.getLogger(__name__)
    
    logger.info("=" * 60)
    logger.info("降雨空间插值系统启动")
    logger.info("=" * 60)
    
    try:
        # 确定要运行的方法
        if args.method == 'all':
            methods = ['IDW', 'Kriging', 'OI', 'PRISM']
        else:
            methods = [args.method]
        
        # 确定要处理的洪水事件
        data_loader = DataLoader(config)
        if args.flood == 'all':
            flood_events = data_loader.get_available_flood_events()
        else:
            flood_events = [args.flood]
        
        if not flood_events:
            logger.error("没有找到可用的洪水事件")
            return
        
        logger.info(f"将处理 {len(methods)} 种方法 × {len(flood_events)} 个洪水事件")
        
        # 执行插值
        all_results = []
        total_tasks = len(methods) * len(flood_events)
        current_task = 0
        
        for method in methods:
            for flood_event in flood_events:
                current_task += 1
                logger.info(f"任务 {current_task}/{total_tasks}: {method} - {flood_event}")
                
                try:
                    result = run_single_method_single_flood(method, flood_event, config)
                    all_results.append(result)
                    
                except Exception as e:
                    logger.error(f"任务失败: {e}")
                    continue
        
        # 保存评估结果
        if all_results:
            validator = ModelValidator(config)
            output_dir = Path(config['project_paths']['base_dir']) / 'output'
            
            summary_file = validator.save_validation_results(all_results, output_dir)
            detailed_file = validator.create_detailed_report(all_results, output_dir)
            
            logger.info("=" * 60)
            logger.info("所有任务完成！")
            logger.info(f"评估摘要: {summary_file}")
            logger.info(f"详细报告: {detailed_file}")
            logger.info("=" * 60)
        
    except Exception as e:
        logger.error(f"程序执行失败: {e}")
        sys.exit(1)


if __name__ == '__main__':
    main()
