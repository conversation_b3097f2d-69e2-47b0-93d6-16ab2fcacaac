# 降雨空间插值系统

一个功能强大、可配置的Python降雨空间插值系统，支持IDW、Kriging、OI、PRISM四种插值方法，具备参数自动优化和高性能并行计算能力。

## 功能特点

- **四种插值方法**: IDW、Kriging、OI、PRISM
- **参数自动优化**: 基于SCE-UA算法的参数自动校准
- **高性能计算**: 多核CPU并行计算和共享内存优化
- **动态站点选择**: 基于Delaunay三角网和莫兰指数的智能站点选择
- **全面评估**: RMSE、MAE、R²、NSE等多种评估指标
- **批处理支持**: 支持多方法、多洪水事件的批量处理
- **栅格输出**: 可选的ASC格式栅格文件输出

## 系统要求

- Python 3.8+
- 8GB+ 内存（推荐）
- 多核CPU（推荐）

## 安装指南

### 1. 创建conda环境（推荐）

```bash
conda create -n rainfall_interp python=3.9
conda activate rainfall_interp
```

### 2. 安装地理空间依赖

```bash
# 使用conda安装地理空间包（推荐）
conda install -c conda-forge pandas numpy rasterio pykrige pyyaml matplotlib

# 或使用pip安装
pip install -r requirements.txt
```

### 3. 可选依赖

```bash
# SCE-UA优化算法（如果安装失败会使用简化算法）
pip install sceua

# 其他可选包
pip install tqdm psutil seaborn
```

## 数据准备

确保您的数据按以下结构组织：

```
D:/pythondata/jiangyuchazhi/
├── input_another/
│   └── 2009-1/          # 洪水事件文件夹
│       ├── 3633.csv     # 站点降雨数据
│       ├── 3634.csv
│       └── ...
├── terrain/
│   └── 90/
│       ├── DEM.asc      # 数字高程模型
│       ├── aspect.asc   # 坡向
│       ├── mask.asc     # 流域掩膜
│       └── slope.asc    # 坡度
├── stations.csv         # 站点信息
└── Delaunay_molan.csv   # 站点选择指导文件
```

### 数据格式要求

1. **站点降雨数据** (如3633.csv):
   - 列名: "时间", "雨量"
   - 编码: UTF-8

2. **站点信息** (stations.csv):
   - 列名: "站点编号", "经度", "纬度", "NAME"
   - 编码: UTF-8

3. **站点选择文件** (Delaunay_molan.csv):
   - 列名: "target_station", "significance_level", "interp_stations_info"
   - interp_stations_info: 逗号分隔的站点编号列表

## 使用方法

### 配置系统

编辑 `config/config.yml` 文件，设置路径、参数和运行选项：

```yaml
project_paths:
  base_dir: "D:/pythondata/jiangyuchazhi/"
  
run_settings:
  methods_to_run: ['all']  # 或 ['IDW', 'Kriging']
  floods_to_run: ['all']   # 或 ['2009-1', '2009-2']
  cpu_cores: 8
  generate_raster_output: true

optimization_settings:
  enable_optimization: true
  methods_to_optimize: ['Kriging', 'OI', 'PRISM']
```

### 运行插值

#### 1. 单一方法，单一洪水事件
```bash
python run.py --method PRISM --flood 2009-1
```

#### 2. 单一方法，所有洪水事件
```bash
python run.py --method PRISM --flood all
```

#### 3. 所有方法，单一洪水事件
```bash
python run.py --method all --flood 2009-1
```

#### 4. 所有方法，所有洪水事件（批处理模式）
```bash
python run.py --method all --flood all
```

### 输出结果

- **栅格文件**: `output/{method}/{flood_event}/rainfall_*.asc`
- **评估摘要**: `output/evaluation_summary.csv`
- **详细报告**: `output/detailed_validation_report.csv`
- **日志文件**: `interpolation.log`

## 配置参数说明

### 插值方法参数

- **IDW**: `power` - 距离权重指数
- **Kriging**: `variogram_model`, `variogram_parameters` - 变异函数模型和参数
- **OI**: `observation_error_variance`, `background_error_corr_length` - 观测误差和相关长度
- **PRISM**: `search_radius`, `distance_power`, `elevation_power` - 搜索半径和权重指数

### 优化设置

- `enable_optimization`: 是否启用参数优化
- `methods_to_optimize`: 需要优化的方法列表
- `max_iterations`: 最大迭代次数
- `time_limit`: 优化时间限制（秒）

### 性能设置

- `cpu_cores`: 使用的CPU核心数
- `use_shared_memory`: 是否使用共享内存
- `batch_size`: 批处理大小

## 评估指标

系统计算以下评估指标：

- **RMSE**: 均方根误差
- **MAE**: 平均绝对误差  
- **R²**: 决定系数
- **NSE**: 纳什效率系数
- **Bias**: 平均偏差

## 故障排除

### 常见问题

1. **地理空间包安装失败**
   ```bash
   # 使用conda安装
   conda install -c conda-forge rasterio geopandas
   ```

2. **内存不足**
   - 减少 `cpu_cores` 数量
   - 设置 `use_shared_memory: false`
   - 减少 `batch_size`

3. **优化失败**
   - 检查 `sceua` 包是否正确安装
   - 系统会自动降级到简化优化算法

4. **数据格式错误**
   - 确保CSV文件使用UTF-8编码
   - 检查列名是否为中文
   - 验证坐标数据的有效性

### 调试模式

在配置文件中设置 `debug_mode: true` 启用详细日志输出。

## 技术支持

如遇问题，请检查：
1. 日志文件 `interpolation.log`
2. 数据格式和路径设置
3. 依赖包安装状态

## 许可证

本项目基于MIT许可证开源。
