# -*- coding: utf-8 -*-
"""
PRISM插值方法实现
Parameter-elevation Regressions on Independent Slopes Model
基于地形的移动窗口回归插值
"""

import numpy as np
from scipy.spatial.distance import cdist
from scipy.spatial import KDTree
from sklearn.linear_model import LinearRegression
from typing import Dict, Tuple, Optional, List
import logging

logger = logging.getLogger(__name__)


class PRISMInterpolator:
    """PRISM插值器"""
    
    def __init__(self, config: Dict):
        """
        初始化PRISM插值器
        
        Args:
            config: 配置字典
        """
        self.config = config
        self.method_params = config['method_params']['PRISM']
        
        # PRISM参数
        self.search_radius = self.method_params['search_radius']
        self.min_stations = self.method_params['min_stations']
        self.max_stations = self.method_params['max_stations']
        self.distance_power = self.method_params['distance_power']
        self.elevation_power = self.method_params['elevation_power']
        
        # 当前优化的参数（如果有）
        self._optimized_params = None
        
        # 地形数据缓存
        self._terrain_data = None
    
    def set_terrain_data(self, dem: np.ndarray, slope: np.ndarray, 
                        aspect: np.ndarray, grid_x: np.ndarray, grid_y: np.ndarray):
        """
        设置地形数据
        
        Args:
            dem: 数字高程模型
            slope: 坡度数据
            aspect: 坡向数据
            grid_x: 网格x坐标
            grid_y: 网格y坐标
        """
        self._terrain_data = {
            'dem': dem,
            'slope': slope,
            'aspect': aspect,
            'grid_x': grid_x,
            'grid_y': grid_y
        }
    
    def set_optimized_parameters(self, params: List[float]) -> None:
        """
        设置优化后的参数
        
        Args:
            params: [search_radius, distance_power, elevation_power] 参数列表
        """
        if len(params) != 3:
            raise ValueError("PRISM参数必须包含3个值: [search_radius, distance_power, elevation_power]")
        
        self._optimized_params = params
        logger.debug(f"设置PRISM优化参数: radius={params[0]:.1f}, dist_power={params[1]:.2f}, elev_power={params[2]:.2f}")
    
    def interpolate(self, station_coords: np.ndarray, station_values: np.ndarray,
                   grid_x: np.ndarray, grid_y: np.ndarray, 
                   mask: np.ndarray) -> np.ndarray:
        """
        执行PRISM插值
        
        Args:
            station_coords: 站点坐标数组 (n_stations, 2)
            station_values: 站点值数组 (n_stations,)
            grid_x: 栅格x坐标
            grid_y: 栅格y坐标
            mask: 掩膜数组
            
        Returns:
            插值结果数组
        """
        if len(station_coords) == 0 or len(station_values) == 0:
            logger.warning("没有有效的站点数据进行PRISM插值")
            return np.full_like(grid_x, np.nan)
        
        # 检查是否所有站点值都为零
        if np.all(station_values == 0):
            logger.debug("所有站点值为零，返回零值栅格")
            return np.zeros_like(grid_x)
        
        # 移除无效值
        valid_mask = ~np.isnan(station_values)
        if not np.any(valid_mask):
            logger.warning("所有站点值都是NaN")
            return np.full_like(grid_x, np.nan)
        
        station_coords = station_coords[valid_mask]
        station_values = station_values[valid_mask]
        
        # 检查站点数量
        if len(station_coords) < self.min_stations:
            logger.warning(f"站点数量({len(station_coords)})少于最小要求({self.min_stations})")
            return self._fallback_to_idw(station_coords, station_values, grid_x, grid_y, mask)
        
        # 检查地形数据
        if self._terrain_data is None:
            logger.error("未设置地形数据，无法进行PRISM插值")
            return self._fallback_to_idw(station_coords, station_values, grid_x, grid_y, mask)
        
        try:
            return self._perform_prism(station_coords, station_values, grid_x, grid_y, mask)
        
        except Exception as e:
            logger.error(f"PRISM插值失败: {e}")
            logger.info("降级使用IDW方法")
            return self._fallback_to_idw(station_coords, station_values, grid_x, grid_y, mask)
    
    def _perform_prism(self, station_coords: np.ndarray, station_values: np.ndarray,
                      grid_x: np.ndarray, grid_y: np.ndarray, 
                      mask: np.ndarray) -> np.ndarray:
        """
        执行PRISM插值的核心逻辑
        """
        # 获取站点高程
        station_elevations = self._get_station_elevations(station_coords)
        
        # 构建KDTree用于快速邻域搜索
        kdtree = KDTree(station_coords)
        
        # 初始化结果数组
        result = np.full_like(grid_x, np.nan)
        
        # 获取有效区域
        valid_mask = ~np.isnan(mask) & (mask != 0)
        valid_indices = np.where(valid_mask)
        
        # 获取当前参数
        if self._optimized_params is not None:
            search_radius, distance_power, elevation_power = self._optimized_params
        else:
            search_radius = self.search_radius
            distance_power = self.distance_power
            elevation_power = self.elevation_power
        
        # 对每个有效网格点进行插值
        for i, j in zip(valid_indices[0], valid_indices[1]):
            grid_point = np.array([grid_x[i, j], grid_y[i, j]])
            grid_elevation = self._terrain_data['dem'][i, j]
            
            if np.isnan(grid_elevation):
                continue
            
            # 查找邻近站点
            neighbor_indices = kdtree.query_ball_point(grid_point, search_radius)
            
            if len(neighbor_indices) < self.min_stations:
                # 如果邻近站点太少，扩大搜索范围
                distances, neighbor_indices = kdtree.query(grid_point, 
                                                         k=min(self.min_stations, len(station_coords)))
                neighbor_indices = neighbor_indices.tolist()
            
            # 限制最大站点数
            if len(neighbor_indices) > self.max_stations:
                distances = np.linalg.norm(station_coords[neighbor_indices] - grid_point, axis=1)
                sorted_indices = np.argsort(distances)
                neighbor_indices = [neighbor_indices[idx] for idx in sorted_indices[:self.max_stations]]
            
            # 获取邻近站点信息
            neighbor_coords = station_coords[neighbor_indices]
            neighbor_values = station_values[neighbor_indices]
            neighbor_elevations = station_elevations[neighbor_indices]
            
            # 计算权重
            weights = self._calculate_prism_weights(
                grid_point, grid_elevation, i, j,
                neighbor_coords, neighbor_elevations,
                distance_power, elevation_power
            )
            
            # 执行加权线性回归
            predicted_value = self._weighted_regression(
                neighbor_elevations, neighbor_values, weights, grid_elevation
            )
            
            result[i, j] = predicted_value
        
        return result
    
    def _get_station_elevations(self, station_coords: np.ndarray) -> np.ndarray:
        """
        获取站点高程值
        """
        dem = self._terrain_data['dem']
        grid_x = self._terrain_data['grid_x']
        grid_y = self._terrain_data['grid_y']
        
        station_elevations = np.zeros(len(station_coords))
        
        for i, (x, y) in enumerate(station_coords):
            # 找到最近的网格点
            distances = np.sqrt((grid_x - x)**2 + (grid_y - y)**2)
            min_idx = np.unravel_index(np.argmin(distances), distances.shape)
            station_elevations[i] = dem[min_idx]
        
        return station_elevations
    
    def _calculate_prism_weights(self, grid_point: np.ndarray, grid_elevation: float,
                               grid_i: int, grid_j: int,
                               neighbor_coords: np.ndarray, neighbor_elevations: np.ndarray,
                               distance_power: float, elevation_power: float) -> np.ndarray:
        """
        计算PRISM权重
        """
        n_neighbors = len(neighbor_coords)
        
        # 1. 距离权重 (Wd)
        distances = np.linalg.norm(neighbor_coords - grid_point, axis=1)
        distances = np.maximum(distances, 1e-10)  # 避免除零
        distance_weights = 1.0 / (distances ** distance_power)
        
        # 2. 高程权重 (Wz)
        elevation_diffs = np.abs(neighbor_elevations - grid_elevation)
        elevation_weights = 1.0 / (1.0 + elevation_diffs ** elevation_power)
        
        # 3. 地形坡面权重 (Wf) - 简化版本
        facet_weights = self._calculate_facet_weights(grid_i, grid_j, neighbor_coords)
        
        # 4. 聚类权重 (Wc) - 简化版本
        cluster_weights = self._calculate_cluster_weights(neighbor_coords)
        
        # 综合权重计算
        # W = Wc * sqrt(Fd * Wd^2 + Fz * Wz^2) * Wf
        # 简化为: W = Wc * Wd * Wz * Wf
        
        combined_weights = cluster_weights * distance_weights * elevation_weights * facet_weights
        
        # 归一化权重
        weight_sum = np.sum(combined_weights)
        if weight_sum > 0:
            combined_weights = combined_weights / weight_sum
        else:
            combined_weights = np.ones(n_neighbors) / n_neighbors
        
        return combined_weights
    
    def _calculate_facet_weights(self, grid_i: int, grid_j: int, 
                               neighbor_coords: np.ndarray) -> np.ndarray:
        """
        计算地形坡面权重（简化版本）
        """
        # 获取网格点的坡向
        grid_aspect = self._terrain_data['aspect'][grid_i, grid_j]
        
        if np.isnan(grid_aspect):
            return np.ones(len(neighbor_coords))
        
        # 获取邻近站点的坡向
        neighbor_aspects = np.zeros(len(neighbor_coords))
        grid_x = self._terrain_data['grid_x']
        grid_y = self._terrain_data['grid_y']
        aspect_data = self._terrain_data['aspect']
        
        for i, (x, y) in enumerate(neighbor_coords):
            distances = np.sqrt((grid_x - x)**2 + (grid_y - y)**2)
            min_idx = np.unravel_index(np.argmin(distances), distances.shape)
            neighbor_aspects[i] = aspect_data[min_idx]
        
        # 计算坡向差异权重
        aspect_diffs = np.abs(neighbor_aspects - grid_aspect)
        # 处理角度环形特性（0度和360度相近）
        aspect_diffs = np.minimum(aspect_diffs, 360 - aspect_diffs)
        
        # 转换为权重（坡向越相似权重越高）
        facet_weights = np.exp(-aspect_diffs / 90.0)  # 90度为半衰期
        
        return facet_weights
    
    def _calculate_cluster_weights(self, neighbor_coords: np.ndarray) -> np.ndarray:
        """
        计算聚类权重（简化版本）
        """
        n_neighbors = len(neighbor_coords)
        cluster_weights = np.ones(n_neighbors)
        
        if n_neighbors <= 1:
            return cluster_weights
        
        # 计算站点间距离
        distances = cdist(neighbor_coords, neighbor_coords)
        
        # 对于每个站点，计算其与其他站点的平均距离
        for i in range(n_neighbors):
            other_distances = distances[i, :]
            other_distances = other_distances[other_distances > 0]  # 排除自身
            
            if len(other_distances) > 0:
                avg_distance = np.mean(other_distances)
                # 距离其他站点越远，聚类权重越高
                cluster_weights[i] = 1.0 + avg_distance / 10000.0  # 归一化因子
        
        return cluster_weights
    
    def _weighted_regression(self, elevations: np.ndarray, values: np.ndarray,
                           weights: np.ndarray, target_elevation: float) -> float:
        """
        执行加权线性回归
        """
        if len(elevations) < 2:
            # 如果只有一个点，直接返回其值
            return values[0] if len(values) > 0 else 0.0
        
        try:
            # 准备回归数据
            X = elevations.reshape(-1, 1)
            y = values
            
            # 使用加权最小二乘法
            # 这里简化为使用sklearn的LinearRegression，然后手动应用权重
            reg = LinearRegression()
            
            # 应用权重（通过重复样本来模拟权重）
            weighted_X = []
            weighted_y = []
            
            for i in range(len(X)):
                weight = max(1, int(weights[i] * 100))  # 转换为整数权重
                weighted_X.extend([X[i]] * weight)
                weighted_y.extend([y[i]] * weight)
            
            if len(weighted_X) == 0:
                return np.mean(values)
            
            weighted_X = np.array(weighted_X).reshape(-1, 1)
            weighted_y = np.array(weighted_y)
            
            reg.fit(weighted_X, weighted_y)
            
            # 预测目标高程的值
            predicted_value = reg.predict([[target_elevation]])[0]
            
            return predicted_value
            
        except Exception as e:
            logger.debug(f"加权回归失败: {e}")
            # 降级到加权平均
            return np.average(values, weights=weights)
    
    def _fallback_to_idw(self, station_coords: np.ndarray, station_values: np.ndarray,
                        grid_x: np.ndarray, grid_y: np.ndarray, 
                        mask: np.ndarray) -> np.ndarray:
        """
        降级到IDW方法
        """
        from .idw import IDWInterpolator
        
        idw_config = {
            'method_params': {'IDW': {'power': 2.0}},
            'run_settings': {'cpu_cores': 1},
            'data_processing': {'memory_management': {'use_shared_memory': False}}
        }
        
        idw_interpolator = IDWInterpolator(idw_config)
        return idw_interpolator.interpolate(station_coords, station_values, grid_x, grid_y, mask)
    
    def get_method_name(self) -> str:
        """获取方法名称"""
        return "PRISM"
    
    def get_parameters(self) -> Dict:
        """获取当前参数"""
        if self._optimized_params is not None:
            search_radius, distance_power, elevation_power = self._optimized_params
        else:
            search_radius = self.search_radius
            distance_power = self.distance_power
            elevation_power = self.elevation_power
        
        return {
            'search_radius': search_radius,
            'min_stations': self.min_stations,
            'max_stations': self.max_stations,
            'distance_power': distance_power,
            'elevation_power': elevation_power
        }
    
    def validate_parameters(self, params: List[float]) -> bool:
        """
        验证参数的有效性
        """
        if len(params) != 3:
            return False
        
        search_radius, distance_power, elevation_power = params
        
        # 基本约束
        if search_radius <= 0 or distance_power <= 0 or elevation_power <= 0:
            return False
        
        return True
