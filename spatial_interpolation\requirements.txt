# 降雨空间插值系统依赖包
# 建议使用conda环境安装地理空间相关包

# 核心科学计算包
numpy>=1.21.0
pandas>=1.3.0
scipy>=1.7.0
scikit-learn>=1.0.0

# 地理空间数据处理
rasterio>=1.2.0
geopandas>=0.10.0
shapely>=1.8.0

# 克里金插值
PyKrige>=1.6.0

# 参数优化（可选，如果安装失败会使用简化算法）
sceua>=0.1.0

# 配置文件处理
PyYAML>=5.4.0

# 可视化（可选）
matplotlib>=3.5.0
seaborn>=0.11.0

# 进度条（可选）
tqdm>=4.62.0

# 内存分析（可选，用于调试）
psutil>=5.8.0

# 注意：
# 1. 在Windows上建议使用conda安装地理空间包：
#    conda install -c conda-forge rasterio geopandas pykrige
# 2. sceua包如果安装失败，系统会自动使用简化的优化算法
# 3. 所有可选包都不是必需的，系统会自动检测并适配
