# -*- coding: utf-8 -*-
"""
克里金插值(<PERSON><PERSON><PERSON>)方法实现
基于PyKrige库，支持参数优化
"""

import numpy as np
from typing import Dict, Tuple, Optional, List
import logging
from pykrige.ok import OrdinaryKriging
from pykrige import variogram_models
import warnings

logger = logging.getLogger(__name__)


class KrigingInterpolator:
    """克里金插值器"""
    
    def __init__(self, config: Dict):
        """
        初始化Kriging插值器
        
        Args:
            config: 配置字典
        """
        self.config = config
        self.method_params = config['method_params']['Kriging']
        
        # 变异函数模型
        self.variogram_model = self.method_params['variogram_model']
        self.variogram_parameters = self.method_params['variogram_parameters']
        
        # 各向异性参数
        self.anisotropy_scaling = self.method_params['anisotropy_scaling']
        self.anisotropy_angle = self.method_params['anisotropy_angle']
        
        # 是否使用伪逆
        self.pseudo_inv = self.method_params.get('pseudo_inv', False)
        
        # 当前优化的参数（如果有）
        self._optimized_params = None
    
    def set_optimized_parameters(self, params: List[float]) -> None:
        """
        设置优化后的参数
        
        Args:
            params: [sill, range, nugget] 参数列表
        """
        if len(params) != 3:
            raise ValueError("Kriging参数必须包含3个值: [sill, range, nugget]")
        
        self._optimized_params = params
        logger.debug(f"设置Kriging优化参数: sill={params[0]:.3f}, range={params[1]:.1f}, nugget={params[2]:.3f}")
    
    def interpolate(self, station_coords: np.ndarray, station_values: np.ndarray,
                   grid_x: np.ndarray, grid_y: np.ndarray, 
                   mask: np.ndarray) -> np.ndarray:
        """
        执行Kriging插值
        
        Args:
            station_coords: 站点坐标数组 (n_stations, 2)
            station_values: 站点值数组 (n_stations,)
            grid_x: 栅格x坐标
            grid_y: 栅格y坐标
            mask: 掩膜数组
            
        Returns:
            插值结果数组
        """
        if len(station_coords) == 0 or len(station_values) == 0:
            logger.warning("没有有效的站点数据进行Kriging插值")
            return np.full_like(grid_x, np.nan)
        
        # 检查是否所有站点值都为零
        if np.all(station_values == 0):
            logger.debug("所有站点值为零，返回零值栅格")
            return np.zeros_like(grid_x)
        
        # 移除无效值
        valid_mask = ~np.isnan(station_values)
        if not np.any(valid_mask):
            logger.warning("所有站点值都是NaN")
            return np.full_like(grid_x, np.nan)
        
        station_coords = station_coords[valid_mask]
        station_values = station_values[valid_mask]
        
        # 检查站点数量
        if len(station_coords) < 3:
            logger.warning(f"站点数量太少({len(station_coords)})，无法进行Kriging插值")
            return np.full_like(grid_x, np.nan)
        
        # 检查数据变异性
        if np.std(station_values) < 1e-10:
            logger.warning("站点值变异性太小，返回均值")
            return np.full_like(grid_x, np.mean(station_values))
        
        try:
            return self._perform_kriging(station_coords, station_values, grid_x, grid_y, mask)
        
        except Exception as e:
            logger.error(f"Kriging插值失败: {e}")
            # 降级到IDW方法
            logger.info("降级使用IDW方法")
            return self._fallback_to_idw(station_coords, station_values, grid_x, grid_y, mask)
    
    def _perform_kriging(self, station_coords: np.ndarray, station_values: np.ndarray,
                        grid_x: np.ndarray, grid_y: np.ndarray, 
                        mask: np.ndarray) -> np.ndarray:
        """
        执行Kriging插值的核心逻辑
        """
        # 获取变异函数参数
        if self._optimized_params is not None:
            variogram_parameters = self._optimized_params
        else:
            variogram_parameters = self.variogram_parameters
        
        # 抑制PyKrige的警告
        with warnings.catch_warnings():
            warnings.simplefilter("ignore")
            
            # 创建OrdinaryKriging对象
            OK = OrdinaryKriging(
                x=station_coords[:, 0],
                y=station_coords[:, 1], 
                z=station_values,
                variogram_model=self.variogram_model,
                variogram_parameters=variogram_parameters,
                anisotropy_scaling=self.anisotropy_scaling,
                anisotropy_angle=self.anisotropy_angle,
                pseudo_inv=self.pseudo_inv,
                verbose=False,
                enable_plotting=False
            )
        
        # 准备插值网格
        result = np.full_like(grid_x, np.nan)
        
        # 获取有效区域
        valid_mask = ~np.isnan(mask) & (mask != 0)
        
        if not np.any(valid_mask):
            return result
        
        # 获取有效点的坐标
        valid_indices = np.where(valid_mask)
        grid_x_valid = grid_x[valid_indices]
        grid_y_valid = grid_y[valid_indices]
        
        # 执行插值
        try:
            kriged_values, kriged_variance = OK.execute('points', grid_x_valid, grid_y_valid)
            
            # 将结果放回原始网格
            result[valid_indices] = kriged_values
            
            # 检查结果的合理性
            if np.any(np.isnan(kriged_values)) or np.any(np.isinf(kriged_values)):
                logger.warning("Kriging结果包含NaN或Inf值")
            
            return result
            
        except Exception as e:
            logger.error(f"Kriging执行失败: {e}")
            raise
    
    def _fallback_to_idw(self, station_coords: np.ndarray, station_values: np.ndarray,
                        grid_x: np.ndarray, grid_y: np.ndarray, 
                        mask: np.ndarray) -> np.ndarray:
        """
        降级到IDW方法
        """
        from .idw import IDWInterpolator
        
        # 创建临时IDW配置
        idw_config = {
            'method_params': {'IDW': {'power': 2.0}},
            'run_settings': {'cpu_cores': 1},
            'data_processing': {'memory_management': {'use_shared_memory': False}}
        }
        
        idw_interpolator = IDWInterpolator(idw_config)
        return idw_interpolator.interpolate(station_coords, station_values, grid_x, grid_y, mask)
    
    def calculate_cross_validation_error(self, station_coords: np.ndarray, 
                                       station_values: np.ndarray,
                                       target_coords: np.ndarray,
                                       target_values: np.ndarray) -> float:
        """
        计算交叉验证误差（用于参数优化）
        
        Args:
            station_coords: 插值站点坐标
            station_values: 插值站点值
            target_coords: 目标站点坐标
            target_values: 目标站点真实值
            
        Returns:
            RMSE误差
        """
        if len(station_coords) < 3:
            return float('inf')
        
        try:
            # 使用当前参数进行插值
            if self._optimized_params is not None:
                variogram_parameters = self._optimized_params
            else:
                variogram_parameters = self.variogram_parameters
            
            with warnings.catch_warnings():
                warnings.simplefilter("ignore")
                
                OK = OrdinaryKriging(
                    x=station_coords[:, 0],
                    y=station_coords[:, 1],
                    z=station_values,
                    variogram_model=self.variogram_model,
                    variogram_parameters=variogram_parameters,
                    anisotropy_scaling=self.anisotropy_scaling,
                    anisotropy_angle=self.anisotropy_angle,
                    pseudo_inv=self.pseudo_inv,
                    verbose=False,
                    enable_plotting=False
                )
            
            # 预测目标点
            predicted_values, _ = OK.execute('points', target_coords[:, 0], target_coords[:, 1])
            
            # 计算RMSE
            rmse = np.sqrt(np.mean((predicted_values - target_values) ** 2))
            
            return rmse
            
        except Exception as e:
            logger.debug(f"交叉验证计算失败: {e}")
            return float('inf')
    
    def get_method_name(self) -> str:
        """获取方法名称"""
        return "Kriging"
    
    def get_parameters(self) -> Dict:
        """获取当前参数"""
        current_params = self._optimized_params if self._optimized_params else self.variogram_parameters
        
        return {
            'variogram_model': self.variogram_model,
            'variogram_parameters': current_params,
            'anisotropy_scaling': self.anisotropy_scaling,
            'anisotropy_angle': self.anisotropy_angle,
            'pseudo_inv': self.pseudo_inv
        }
    
    def validate_parameters(self, params: List[float]) -> bool:
        """
        验证参数的有效性
        
        Args:
            params: [sill, range, nugget] 参数列表
            
        Returns:
            参数是否有效
        """
        if len(params) != 3:
            return False
        
        sill, range_param, nugget = params
        
        # 基本约束
        if sill <= 0 or range_param <= 0 or nugget < 0:
            return False
        
        # 块金值不应超过基台值
        if nugget >= sill:
            return False
        
        return True
